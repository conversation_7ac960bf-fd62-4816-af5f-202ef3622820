import { stringify } from 'qs';
import request from '@/utils/request';

/**
 * 查询列表
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function query(params) {
  // return request(`${SERVER_HOST[API_ENV]}/tr_r_list?${stringify(params)}`);
  return request(`/server/api/tr_r_list?${stringify(params)}`);
}

/**
 * 创建
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function create(params) {
  // return request(`${SERVER_HOST[API_ENV]}/createTr_r`, {
  return request(`/server/api/createTr_r`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function update(params) {
  // return request(`${SERVER_HOST[API_ENV]}/updateTr_r`, {
  return request(`/server/api/updateTr_r`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 批量删除
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function removeList(params) {
  // return request(`${SERVER_HOST[API_ENV]}/delTr_r_list`, {
  return request(`/server/api/delTr_r_list`, {
    method: 'POST',
    data: params,
  });
}
