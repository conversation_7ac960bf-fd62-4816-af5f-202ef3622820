import React, { PureComponent } from 'react';
import { Icon } from 'antd';
import Link from 'umi/link';
import Debounce from 'lodash-decorators/debounce';
import styles from './index.less';
import RightContent from './RightContent';
import { shortTitle } from '../../defaultSettings';

export default class GlobalHeader extends PureComponent {
  componentWillUnmount() {
    this.triggerResizeEvent.cancel();
  }
  /* eslint-disable*/
  @Debounce(600)
  triggerResizeEvent() {
    // eslint-disable-line
    const event = document.createEvent('HTMLEvents');
    event.initEvent('resize', true, false);
    window.dispatchEvent(event);
  }
  toggle = () => {
    const { collapsed, onCollapse } = this.props;
    onCollapse(!collapsed);
    this.triggerResizeEvent();
  };
  render() {
    const { collapsed, isMobile, logo, logoTitle } = this.props;
    return (
      <div className={styles.header}>
        <span className={styles.trigger} onClick={this.toggle}>
          <Icon type={collapsed ? 'menu-unfold' : 'menu-fold'} />
        </span>
        {isMobile ? (
          <Link to="/" className={styles.logo} key="logo">
            <img src={logo} alt="logo" width="32" style={{ verticalAlign: 'sub' }} />
            <h1
              style={{
                display: 'inline-block',
                marginLeft: '0.5em',
                color: '#fff',
                verticalAlign: 'middle',
              }}
            >
              {shortTitle}
            </h1>
          </Link>
        ) : (
          <Link
            to="/"
            className={styles.logo}
            key="logo"
            style={{ position: 'absolute', left: '50%', marginLeft: '-290px', padding: '0' }}
          >
            <img src={logoTitle} alt="logo" style={{ height: '3.6em' }} />
            <h1
              style={{
                display: 'inline-block',
                marginLeft: '0.5em',
                color: '#fff',
                verticalAlign: 'middle',
              }}
            >
              {shortTitle}
            </h1>
          </Link>
        )}
        <RightContent {...this.props} />
      </div>
    );
  }
}
