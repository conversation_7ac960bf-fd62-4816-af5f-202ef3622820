{"compilerOptions": {"outDir": "build/dist", "module": "esnext", "target": "es2016", "lib": ["es6", "dom"], "sourceMap": true, "baseUrl": ".", "jsx": "react", "allowSyntheticDefaultImports": true, "moduleResolution": "node", "rootDirs": ["/src", "/test", "/mock", "./typings"], "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "allowJs": true, "experimentalDecorators": true, "paths": {"@/*": ["./src/*"]}}, "include": ["./src"], "exclude": ["node_modules", "build", "scripts", "acceptance-tests", "webpack", "jest", "src/setupTests.ts", "tslint:latest", "tslint-config-prettier"]}