import { stringify } from 'qs';
import request from '@/utils/request';
import { getCurrentUserNum } from '@/utils/authority';

export default {
  /**
   * 获取所有用户列表
   *
   * @param {*} params
   * @returns
   */
  query: async params => {
    // return request(`${SERVER_HOST[API_ENV]}/users?${stringify(params)}`);
    return request(`/server/api/users?${stringify(params)}`);
  },

  /**
   * 获取当前登录用户
   *
   * @returns
   */
  queryCurrent: async () => {
    const num = getCurrentUserNum();
    // return request(`${SERVER_HOST[API_ENV]}/user/${num}`, {
    return request(`/server/api/user/${num}`, {
      requestType: 'json',
    });
  },

  /**
   * 创建用户
   *
   * @param {*} params
   * @returns
   */
  createUser: async params => {
    // return request(`${SERVER_HOST[API_ENV]}/createUser`, {
    return request(`/server/api/createUser`, {
      method: 'POST',
      data: params,
    });
  },

  /**
   * 更新用户
   *
   * @param {*} params
   * @returns
   */
  updateUser: async params => {
    // return request(`${SERVER_HOST[API_ENV]}/updateUser`, {
    return request(`/server/api/updateUser`, {
      method: 'POST',
      data: params,
    });
  },

  /**
   * 批量删除用户
   *
   * @param {*} params
   * @returns
   */
  removeList: async params => {
    // return request(`${SERVER_HOST[API_ENV]}/delUsers`, {
    return request(`/server/api/delUsers`, {
      method: 'POST',
      data: params,
    });
  },
};
