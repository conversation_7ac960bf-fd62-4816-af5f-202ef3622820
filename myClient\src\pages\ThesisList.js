/* eslint-disable no-nested-ternary */
/* eslint-disable camelcase */
/* eslint-disable no-underscore-dangle */
import React, { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import router from 'umi/router';
import moment from 'moment';
import {
  Card,
  Row,
  Col,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Divider,
  Modal,
  message,
} from 'antd';
import StandardTable from '@/components/StandardTable';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';

import styles from './ThesisList.less';

const FormItem = Form.Item;
const { Option } = Select;
const { MonthPicker } = DatePicker;

const dateFormat = 'YYYY/MM/DD';
const monthFormat = 'YYYY/MM';

const { confirm } = Modal;
/**
 * 全局方法，弹出确认窗口
 *
 * @param {String} title 标题
 * @param {String} content 描述
 * @param {Function} onOk 确认回调
 * @param {Function} onCancel 取消回调
 */
function showConfirm(title, content, onOk, onCancel) {
  confirm({
    title,
    content,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      if (typeof onOk === 'function') {
        onOk();
      }
    },
    onCancel: () => {
      if (typeof onCancel === 'function') {
        onCancel();
      }
    },
  });
}

@Form.create()
class UpdateForm extends PureComponent {
  formLayout = {
    layout: 'vertical',
  };

  formItemLayout = {
    style: {
      width: '100%',
    },
  };

  /**
   * 基础信息表单
   *
   * @returns
   * @memberof UpdateForm
   */
  editForm() {
    const {
      form: { getFieldDecorator },
      values,
      infoMode,
      handleSaveInfo,
    } = this.props;

    const hasValue = infoMode === 'edit' || infoMode === 'view';
    const canEdit = infoMode === 'create' || infoMode === 'edit';

    return (
      <Form
        {...this.formLayout}
        onSubmit={e => {
          e.preventDefault();
          const { form } = this.props;
          handleSaveInfo(form, infoMode);
        }}
      >
        <FormItem {...this.formItemLayout}>
          {getFieldDecorator('_id', {
            initialValue: hasValue ? values._id : '',
          })(<Input type="hidden" />)}
        </FormItem>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="人员代码" required>
              {getFieldDecorator('num', {
                initialValue: hasValue ? values.num : '',
                rules: [{ required: true, message: '不能为空！' }],
              })(<Input disabled={hasValue} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="姓名">
              {getFieldDecorator('name', {
                initialValue: hasValue ? values.user_id.name : '',
              })(<Input disabled />)}
            </FormItem>
          </Col>
        </Row>
        <Divider orientation="left" />
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={24}>
            <FormItem {...this.formItemLayout} label="论文名称">
              {getFieldDecorator('lwmc', { initialValue: hasValue ? values.lwmc : '' })(
                <Input disabled={!canEdit} />
              )}
            </FormItem>
          </Col>
          <Col span={24}>
            <FormItem {...this.formItemLayout} label="发表期刊">
              {getFieldDecorator('fbqk', {
                initialValue: hasValue ? values.fbqk : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="作者">
              {getFieldDecorator('zz', {
                initialValue: hasValue ? values.zz : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="所在单位">
              {getFieldDecorator('zzbm', {
                initialValue: hasValue ? values.zzbm : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="作者排序">
              {getFieldDecorator('zzbx', {
                initialValue: hasValue ? values.zzbx : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="作者校内排序">
              {getFieldDecorator('zzxnpx', {
                initialValue: hasValue ? values.zzxnpx : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="提交年度">
              {getFieldDecorator('tj_year', {
                initialValue: hasValue ? values.tj_year : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="类型">
              {getFieldDecorator('lx', { initialValue: hasValue ? values.lx : '' })(
                <Input disabled={!canEdit} />
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="卷">
              {getFieldDecorator('j', {
                initialValue: hasValue ? values.j : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="发表时间">
              {getFieldDecorator('fbsj', {
                initialValue:
                  hasValue && values.fbsj ? moment(new Date(values.fbsj), dateFormat) : null,
              })(<MonthPicker format={monthFormat} disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="收录情况">
              {getFieldDecorator('slqk', {
                initialValue: hasValue ? values.slqk : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="期">
              {getFieldDecorator('q', {
                initialValue: hasValue ? values.q : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="国外核心期刊">
              {getFieldDecorator('gwhxqk', {
                initialValue: hasValue ? values.gwhxqk : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="国外一般期刊">
              {getFieldDecorator('gwybqk', {
                initialValue: hasValue ? values.gwybqk : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="通讯作者">
              {getFieldDecorator('txzz', {
                initialValue: hasValue ? values.txzz : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="国内重要期刊">
              {getFieldDecorator('gnzyqk', {
                initialValue: hasValue ? values.gnzyqk : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="第一作者">
              {getFieldDecorator('dyzz', {
                initialValue: hasValue ? values.dyzz : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="第一作者是否为在籍学生">
              {getFieldDecorator('dyzz_zjxs', {
                initialValue: hasValue ? (values.dyzz_zjxs ? 1 : 0) : '',
              })(
                <Select disabled={!canEdit}>
                  <Option value={1}>是</Option>
                  <Option value={0}>否</Option>
                </Select>
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="学生学号">
              {getFieldDecorator('xsxh', {
                initialValue: hasValue ? values.xsxh : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="学生姓名">
              {getFieldDecorator('xsxm', {
                initialValue: hasValue ? values.xsxm : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="重要期刊主办单位">
              {getFieldDecorator('zyqkzbdw', {
                initialValue: hasValue ? values.zyqkzbdw : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="国外其他刊物">
              {getFieldDecorator('gwqtkw', {
                initialValue: hasValue ? values.gwqtkw : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="论文类型">
              {getFieldDecorator('lwlx', {
                initialValue: hasValue ? values.lwlx : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="CSSCI刊源期刊">
              {getFieldDecorator('cssci_kyqk', {
                initialValue: hasValue ? values.cssci_kyqk : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="国内中文核心期刊">
              {getFieldDecorator('gnzwhxqk', {
                initialValue: hasValue ? values.gnzwhxqk : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="CSCD刊源期刊">
              {getFieldDecorator('cscd_kyqk', {
                initialValue: hasValue ? values.cscd_kyqk : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="ISSN">
              {getFieldDecorator('issn', {
                initialValue: hasValue ? values.issn : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="增刊还是正刊">
              {getFieldDecorator('zeng_zheng', {
                initialValue: hasValue ? values.zeng_zheng : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="语言">
              {getFieldDecorator('yy', {
                initialValue: hasValue ? values.yy : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="影响因子">
              {getFieldDecorator('yxyz', {
                initialValue: hasValue ? values.yxyz : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="论文统计类型">
              {getFieldDecorator('lwlx_custom', {
                initialValue: hasValue ? values.lwlx_custom : '',
              })(
                <Select disabled={!canEdit}>
                  <Option value="">--请选择--</Option>
                  <Option value={0}>SCI/C刊</Option>
                  <Option value={1}>SCI一区</Option>
                  <Option value={2}>SCI二区</Option>
                </Select>
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="备注">
              {getFieldDecorator('remark', { initialValue: hasValue ? values.remark : '' })(
                <Input disabled={!canEdit} />
              )}
            </FormItem>
          </Col>
        </Row>

        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={24} style={{ textAlign: 'right' }}>
            {canEdit ? (
              <Button type="primary" style={{ margin: '1em', width: '10em' }} htmlType="submit">
                保存
              </Button>
            ) : (
              ''
            )}
          </Col>
        </Row>
      </Form>
    );
  }

  render() {
    const { visible, title, values, handleUpdateModalVisible } = this.props;

    return (
      <Modal
        className={styles.updateForm}
        width={640}
        bodyStyle={{ padding: '32px 40px 48px' }}
        destroyOnClose
        title={title}
        visible={visible}
        maskClosable={false}
        footer={null}
        onCancel={() => handleUpdateModalVisible(false, title, values)}
      >
        {this.editForm()}
      </Modal>
    );
  }
}

/* eslint react/no-multi-comp:0 */
@connect(({ thesis_r, loading }) => ({
  thesis_r,
  loading: loading.models.thesis_r,
}))
@Form.create()
class TableList extends PureComponent {
  state = {
    selectedRows: [], // 选中行
    updateModal: {
      // 信息窗口
      visible: false, // 显示状态
      title: '', // 标题
      values: {}, // 内容
      infoMode: 'create', // 基础信息编辑状态, create|edit|view
    },
  };

  // 表列配置
  columns = [
    {
      title: '人员代码',
      dataIndex: 'num',
      // sorter: true,
      render: text => (
        <a
          onClick={() => {
            router.push(`/jbqkb?num=${text}`);
          }}
        >
          {text}
        </a>
      ),
    },
    {
      title: '姓名',
      dataIndex: 'user_id.name',
      // sorter: true,
    },
    {
      title: '所在单位',
      dataIndex: 'user_id.college',
    },
    {
      title: '收录情况',
      dataIndex: 'slqk',
    },
    {
      title: '发表时间',
      dataIndex: 'fbsj',
      render: text => {
        if (text) {
          const time = new Date(text);
          return `${time.getFullYear()}年${time.getMonth() + 1}月${time.getDate()}日`;
        }
        return '';
      },
    },
    {
      title: '操作',
      render: (text, record) => (
        <Fragment>
          <a onClick={() => this.handleUpdateModalVisible(true, '论文成果详情', record, 'view')}>
            查看
          </a>
          <Divider type="vertical" />
          <a onClick={() => this.handleUpdateModalVisible(true, '论文成果详情', record, 'edit')}>
            修改
          </a>
          <Divider type="vertical" />
          <a
            onClick={e => {
              e.preventDefault();
              this.handlerRemove([record._id]);
            }}
          >
            删除
          </a>
        </Fragment>
      ),
    },
  ];

  componentDidMount() {
    const {
      dispatch,
      thesis_r: {
        data: { pagination },
      },
    } = this.props;
    dispatch({
      type: 'thesis_r/getList',
      payload: pagination,
    });
  }

  /**
   * 控制编辑用户信息组件显示状态
   *
   * @param {*} flag
   * @param {*} title
   * @param {*} record
   * @param {*} infoMode
   */
  handleUpdateModalVisible = (flag, title, record, infoMode) => {
    const updateModal = {
      visible: !!flag,
      title,
      values: record || {},
      infoMode: infoMode || 'create',
    };
    this.setState({
      updateModal,
    });
  };

  /**
   * 保存用户基础信息，自动判断新增是更新
   *
   * @param {Object} form 表单对象
   * @param {String} infoMode 提交模式，新增或更新
   */
  handleSaveInfo = (form, infoMode) => {
    const {
      dispatch,
      thesis_r: {
        data: { pagination },
      },
    } = this.props;

    if (form) {
      form.validateFields((err, fieldsValue) => {
        if (err) return;

        const values = {
          ...fieldsValue,
        };

        const saveUrl = infoMode === 'edit' ? 'thesis_r/update' : 'thesis_r/create';
        dispatch({
          type: saveUrl,
          payload: values,
          callback: result => {
            if (result.status === 'ok') {
              message.success('保存成功');
              dispatch({
                type: 'thesis_r/getList',
                payload: pagination,
              });
              if (infoMode === 'create') {
                const { updateModal } = this.state;
                this.setState({
                  updateModal: {
                    ...updateModal,
                    title: '论文成果详情',
                    values: result.result,
                    infoMode: 'edit',
                  },
                });
              }
            } else {
              message.error(result.message);
            }
          },
        });
      });
    }
  };

  /**
   * 选中行
   *
   * @param {Array} rows
   */
  handleSelectRows = rows => {
    this.setState({
      selectedRows: rows,
    });
  };

  /**
   * 模糊查询
   *
   * @param {Event} e
   */
  handleSearch = e => {
    e.preventDefault();

    const {
      dispatch,
      form,
      thesis_r: {
        data: { pagination },
      },
    } = this.props;

    form.validateFields((err, fieldsValue) => {
      if (err) return;

      const values = {
        ...fieldsValue,
        updatedAt: fieldsValue.updatedAt && fieldsValue.updatedAt.valueOf(),
      };

      dispatch({
        type: 'thesis_r/getList',
        payload: Object.assign({}, pagination, values.name && values),
      });
    });
  };

  /**
   * 排序，筛选，翻页
   *
   * @memberof TableList
   */
  handleStandardTableChange = (pagination, filtersArg, sorter) => {
    const { dispatch } = this.props;
    const { formValues } = this.state;

    // TODO: 筛选未实现
    // const filters = Object.keys(filtersArg).reduce((obj, key) => {
    //   const newObj = { ...obj };
    //   newObj[key] = getValue(filtersArg[key]);
    //   return newObj;
    // }, {});

    const params = {
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
      ...formValues,
      // ...filters,
    };
    if (sorter.field) {
      params.sorter = `${sorter.field}_${sorter.order}`;
    }

    dispatch({
      type: 'thesis_r/getList',
      payload: params,
    });
  };

  /**
   * 删除
   *
   * @param {Array} list
   */
  handlerRemove = list => {
    const {
      dispatch,
      thesis_r: {
        data: { pagination },
      },
    } = this.props;
    const { selectedRows } = this.state;
    const _idList =
      list ||
      selectedRows.map(row => {
        return row._id;
      });

    showConfirm('是否确认删除', '删除操作不可逆，请谨慎操作！', () => {
      dispatch({
        type: 'thesis_r/removeList',
        payload: { _idList },
        callback: () => {
          message.success('删除成功');
          dispatch({
            type: 'thesis_r/getList',
            payload: pagination,
          });
        },
      });
    });
  };

  /**
   * 模糊查询组件
   *
   * @returns
   * @memberof TableList
   */
  serchForm() {
    const {
      form: { getFieldDecorator },
    } = this.props;

    return (
      <Form onSubmit={this.handleSearch} layout="inline">
        <FormItem>
          {getFieldDecorator('name')(<Input placeholder="请输入人员代码或姓名" />)}
        </FormItem>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
      </Form>
    );
  }

  render() {
    const {
      thesis_r: { data },
      loading,
    } = this.props;
    const { selectedRows, updateModal } = this.state;

    // 弹出窗的相关方法
    const updateMethods = {
      handleUpdateModalVisible: this.handleUpdateModalVisible, // 切换显示状态
      handleSaveInfo: this.handleSaveInfo, // 保存基础信息
    };

    return (
      <PageHeaderWrapper title="论文成果表">
        <Card bordered={false}>
          <div className={styles.tableList}>
            <Row>
              <Col md={8} sm={24}>
                <div className={styles.tableListOperator}>
                  <Button
                    icon="plus"
                    type="primary"
                    onClick={() => this.handleUpdateModalVisible(true, '新建记录')}
                  >
                    新建
                  </Button>
                  {selectedRows.length > 0 && (
                    <span>
                      <Button onClick={() => this.handlerRemove()}>批量删除</Button>
                    </span>
                  )}
                </div>
              </Col>
              <Col md={16} sm={24}>
                <div className={styles.tableListForm}>{this.serchForm()}</div>
              </Col>
            </Row>

            <StandardTable
              className={styles.tableListTable}
              rowKey="_id"
              selectedRows={selectedRows}
              loading={loading}
              data={data}
              columns={this.columns}
              hasrowSelection={false}
              onSelectRow={this.handleSelectRows}
              onChange={this.handleStandardTableChange}
            />
          </div>
        </Card>
        <UpdateForm {...updateModal} {...updateMethods} />
      </PageHeaderWrapper>
    );
  }
}

export default TableList;
