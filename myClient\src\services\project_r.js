import { stringify } from 'qs';
import request from '@/utils/request';

/**
 * 查询列表
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function query(params) {
  // return request(`${SERVER_HOST[API_ENV]}/pr_r_list?${stringify(params)}`);
  return request(`/server/api/pr_r_list?${stringify(params)}`);
}

/**
 * 创建
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function create(params) {
  // return request(`${SERVER_HOST[API_ENV]}/createPr_r`, {
  return request(`/server/api/createPr_r`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function update(params) {
  // return request(`${SERVER_HOST[API_ENV]}/updatePr_r`, {
  return request(`/server/api/updatePr_r`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 批量删除
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function removeList(params) {
  // return request(`${SERVER_HOST[API_ENV]}/delPr_r_list`, {
  return request(`/server/api/delPr_r_list`, {
    method: 'POST',
    data: params,
  });
}
