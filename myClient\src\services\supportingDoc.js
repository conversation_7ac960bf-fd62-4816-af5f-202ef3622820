import { stringify } from 'qs';
import request from '@/utils/request';

/**
 * 查询列表
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function query(params) {
  // return request(`${SERVER_HOST[API_ENV]}/supportingDoc_list?${stringify(params)}`);
  return request(`/server/api/supportingDoc_list?${stringify(params)}`);
}

/**
 * 创建
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function create(params) {
  // return request(`${SERVER_HOST[API_ENV]}/createSupportingDoc`, {
  return request(`/server/api/createSupportingDoc`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function update(params) {
  // return request(`${SERVER_HOST[API_ENV]}/updateSupportingDoc`, {
  return request(`/server/api/updateSupportingDoc`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 批量删除
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function removeList(params) {
  // return request(`${SERVER_HOST[API_ENV]}/delSupportingDoc_list`, {
  return request(`/server/api/delSupportingDoc_list`, {
    method: 'POST',
    data: params,
  });
}
