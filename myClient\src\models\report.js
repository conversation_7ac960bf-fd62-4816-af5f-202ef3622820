import { queryBaseInfo, exportBaseInfo, exportToExcel } from '@/services/api';

export default {
  namespace: 'report',

  state: {
    data: {},
  },

  effects: {
    /* ---------------------------------------基本情况表-------------------------------------------- */

    /**
     * 按人员代码查询基本情况表内容
     *
     * @param {*} { payload, callback }
     * @param {*} { call, put }
     */
    *queryBaseInfo({ payload, callback }, { call, put }) {
      const response = yield call(queryBaseInfo, payload);

      if (response.status === 'ok') {
        yield put({
          type: 'save',
          payload: response.info,
        });
      } else {
        callback(response.message);
      }
    },

    /**
     * 导出基本情况表
     *
     * @param {*} { payload, callback }
     * @param {*} { call }
     */
    *exportBaseInfo({ payload, callback }, { call }) {
      const response = yield call(exportBaseInfo, payload);

      callback(response);
    },

    /* ---------------------------------------导出-------------------------------------------- */

    /**
     * 导出
     *
     * @param {*} { payload, callback }
     * @param {*} { call, put }
     */
    *exportToExcel({ payload, callback }, { call }) {
      const response = yield call(exportToExcel, payload);

      callback(response);
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        currentData: action.payload,
      };
    },
  },
};
