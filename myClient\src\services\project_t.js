import { stringify } from 'qs';
import request from '@/utils/request';

export default {
  /**
   * 查询列表
   *
   * @param {*} params
   * @returns
   */
  query: async params => {
    // return request(`${SERVER_HOST[API_ENV]}/pr_t_list?${stringify(params)}`);
    return request(`/server/api/pr_t_list?${stringify(params)}`);
  },

  /**
   * 创建记录
   *
   * @param {*} params
   * @returns
   */
  create: async params => {
    // return request(`${SERVER_HOST[API_ENV]}/createPr_t`, {
    return request(`/server/api/createPr_t`, {
      method: 'POST',
      data: params,
    });
  },

  /**
   * 更新
   *
   * @param {*} params
   * @returns
   */
  update: async params => {
    // return request(`${SERVER_HOST[API_ENV]}/updatePr_t`, {
    return request(`/server/api/updatePr_t`, {
      method: 'POST',
      data: params,
    });
  },

  /**
   * 批量删除
   *
   * @param {*} params
   * @returns
   */
  removeList: async params => {
    // return request(`${SERVER_HOST[API_ENV]}/delPr_t_list`, {
    return request(`/server/api/delPr_t_list`, {
      method: 'POST',
      data: params,
    });
  },
};
