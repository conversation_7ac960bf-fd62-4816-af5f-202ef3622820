/* eslint-disable react/no-string-refs */
/* eslint-disable camelcase */
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Card, Row, Col, Button, Divider, message } from 'antd';
import ReactHTMLTableToExcel from 'react-html-table-to-excel';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';

import styles from './BaseInfo.less';

import { getPageQuery, downloadExport } from '@/utils/utils';
import { getAuthority, getCurrentUserNum } from '@/utils/authority';
import { summary_years } from '../defaultSettings';

/* eslint react/no-multi-comp:0 */
@connect(({ report, loading }) => ({
  report,
  loading: loading.models.report,
}))
class InfoTable extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      years: summary_years,
    };
  }

  componentDidMount() {
    const { dispatch } = this.props;
    const { years } = this.state;

    // 管理员通过url传参获取,普通用户只能获取自身信息
    const num = getAuthority().includes('admin') ? getPageQuery().num : getCurrentUserNum();

    dispatch({
      type: `report/queryBaseInfo`,
      payload: {
        num,
        years,
      },
      callback: msg => {
        message.error(msg);
      },
    });
  }

  exportToExcel = () => {
    const {
      report: { currentData = {} },
      dispatch,
    } = this.props;

    message.success('下载中...');
    dispatch({
      type: `report/exportBaseInfo`,
      payload: {
        years: summary_years,
        currentData,
      },
      callback: result => {
        if (result.status === 'ok') {
          message.success('开始下载...');
          downloadExport(result.fileName);
        } else {
          message.error(result.message);
        }
      },
    });
  };

  render() {
    const {
      report: { currentData = {} },
    } = this.props;

    return (
      <PageHeaderWrapper title="高层次人才基本情况">
        <Card bordered={false}>
          <div className={styles.btnCon}>
            <ReactHTMLTableToExcel
              ref="tableXlsButton"
              id="table-xls-button"
              className={styles.tableXlsButton}
              table="baseInfoTable"
              filename="高层次人才基本情况"
              sheet="高层次人才基本情况"
              buttonText="下载excel"
            />
            <Button
              type="link"
              icon="download"
              style={{
                float: 'right',
                marginTop: '-3px',
                marginRight: '1em',
              }}
              onClick={this.exportToExcel}
              // onClick={() => {
              // 使用 ReactHTMLTableToExcel 插件
              //   self.refs.tableXlsButton.handleDownload();
              // }}
            >
              下载
            </Button>
          </div>
          <Divider />
          <table id="baseInfoTable" className={styles.baseInfoTable}>
            <tbody>
              <tr>
                <td className={styles.noBorder} colSpan={12}>
                  <Row>
                    <Col span={3}>
                      <b>姓名：</b>
                      {currentData && currentData.name}
                    </Col>
                    <Col span={5}>
                      <b>人才层次：</b>
                      {currentData && currentData.enter_level}
                    </Col>
                    <Col span={5}>
                      <b>所在单位：</b>
                      {currentData && currentData.college}
                    </Col>
                    <Col span={6}>
                      <b>入校/选/站时间：</b>
                      {currentData && currentData.enter_date && currentData.enter_date}
                    </Col>
                    <Col span={4}>
                      <b>人员状态：</b>
                      {currentData && currentData.status}
                    </Col>
                  </Row>
                </td>
              </tr>
              <tr>
                <th className={styles.title}>人员代码</th>
                <td>{currentData && currentData.num}</td>
                <th className={styles.title}>性 别</th>
                <td>{currentData && currentData.sex}</td>
                <th className={styles.title}>出生年月</th>
                <td>{currentData && currentData.birthday && currentData.birthday}</td>
                <th className={styles.title}>籍 贯</th>
                <td>{currentData && currentData.native_place}</td>
                <th className={styles.title}>政治面貌</th>
                <td>{currentData && currentData.political_outlook}</td>
                <th className={styles.title}>签约时间</th>
                <td>{currentData && currentData.signing_time && currentData.signing_time}</td>
              </tr>
              <tr>
                <th className={styles.title}>海外博士</th>
                <td>{currentData && currentData.overseas_Dr}</td>
                <th className={styles.title}>职 称</th>
                <td>{currentData && currentData.title}</td>
                <th className={styles.title}>评审时间</th>
                <td>{currentData && currentData.review_time && currentData.review_time}</td>
                <th className={styles.title}>博后经历</th>
                <td>{currentData && currentData.after_Dr}</td>
                <th className={styles.title}>博士专业</th>
                <td colSpan={3}>{currentData && currentData.Dr_major}</td>
              </tr>
              <tr>
                <th className={styles.title}>博士毕业</th>
                <td>{currentData && currentData.graduation_date && currentData.graduation_date}</td>
                <th className={styles.title}>毕业学校</th>
                <td colSpan={3}>{currentData && currentData.Dr_school}</td>
                <th className={styles.title}>博导姓名</th>
                <td>{currentData && currentData.Dr_tutor_name}</td>
                <th className={styles.title}>博导称号</th>
                <td colSpan={3}>{currentData && currentData.Dr_tutor_title}</td>
              </tr>
              <tr>
                <th className={styles.title}>海外经历</th>
                <td>{currentData && currentData.overseas}</td>
                <th className={styles.title}>经历时限</th>
                <td colSpan={5}>{currentData && currentData.overseas_time}</td>
                <th className={styles.title}>联系方式</th>
                <td colSpan={3}>{currentData && currentData.mobile}</td>
              </tr>
              <tr>
                <td colSpan={12} />
              </tr>
              <tr>
                <th className={styles.title} rowSpan={2} colSpan={2}>
                  协议待遇
                </th>
                <th>住 房</th>
                <th>过度住房</th>
                <th>安家费</th>
                <th>科研经费</th>
                <th>实验经费</th>
                <th>科研用房</th>
                <th>购房补贴</th>
                <th>职称特评</th>
                <th colSpan={2}>配偶工作</th>
              </tr>
              <tr>
                <td>{currentData && currentData.housing}</td>
                <td>{currentData && currentData.makeshift_house}</td>
                <td>{currentData && currentData.settling_in_allowance}</td>
                <td>{currentData && currentData.scientific_research_funds}</td>
                <td>{currentData && currentData.lab_construction_fee}</td>
                <td>{currentData && currentData.scientific_research_house}</td>
                <td>{currentData && currentData.housing_subsidies}</td>
                <td>{currentData && currentData.title_tp}</td>
                <td colSpan={2}>{currentData && currentData.spouse_work}</td>
              </tr>
              {summary_years.map((year, index) => {
                return (
                  <tr key={year}>
                    {!index && (
                      <th className={styles.title} rowSpan={summary_years.length}>
                        待遇兑现
                      </th>
                    )}
                    <th className={styles.title}>{year}</th>
                    <td>{currentData && currentData[year] && currentData[year].housing}</td>
                    <td>{currentData && currentData[year] && currentData[year].makeshift_house}</td>
                    <td>
                      {currentData && currentData[year] && currentData[year].settling_in_allowance}
                    </td>
                    <td>
                      {currentData &&
                        currentData[year] &&
                        currentData[year].scientific_research_funds}
                    </td>
                    <td>
                      {currentData && currentData[year] && currentData[year].lab_construction_fee}
                    </td>
                    <td>
                      {currentData &&
                        currentData[year] &&
                        currentData[year].scientific_research_house}
                    </td>
                    <td>
                      {currentData && currentData[year] && currentData[year].housing_subsidies}
                    </td>
                    <td>{currentData && currentData[year] && currentData[year].title_tp}</td>
                    <td colSpan={2}>
                      {currentData && currentData[year] && currentData[year].spouse_work}
                    </td>
                  </tr>
                );
              })}
              <tr>
                <td colSpan={12} />
              </tr>
              <tr>
                <th className={styles.title} rowSpan={3} colSpan={2}>
                  目标任务
                </th>
                <th colSpan={4}>论 文</th>
                <th colSpan={3}>项 目</th>
                <th colSpan={3}>其 他</th>
              </tr>
              <tr>
                <th>总 数</th>
                <th>SCI/C刊</th>
                <th>SCI一区</th>
                <th>SCI二区</th>
                <th>青基时限</th>
                <th>面上时限</th>
                <th>项目经费</th>
                <th>申报人才称号</th>
                <th>推荐人才</th>
                <th>获奖</th>
              </tr>
              <tr>
                <td>{currentData && currentData.sum}</td>
                <td>{currentData && currentData.sci}</td>
                <td>{currentData && currentData.sci1}</td>
                <td>{currentData && currentData.sci2}</td>
                <td>{currentData && currentData.qj_date}</td>
                <td>{currentData && currentData.ms_date}</td>
                <td>{currentData && currentData.jfze}</td>
                <td>{currentData && currentData.sbrcch}</td>
                <td>{currentData && currentData.tjrc}</td>
                <td>{currentData && currentData.hjqk}</td>
              </tr>
              {summary_years.map((year, index) => {
                return (
                  <tr key={year}>
                    {!index && (
                      <th className={styles.title} rowSpan={summary_years.length}>
                        年度业绩成果
                      </th>
                    )}
                    <th className={styles.title}>{year}</th>
                    <td>{currentData && currentData[year] && currentData[year].sum}</td>
                    <td>{currentData && currentData[year] && currentData[year].sci}</td>
                    <td>{currentData && currentData[year] && currentData[year].sci1}</td>
                    <td>{currentData && currentData[year] && currentData[year].sci2}</td>
                    <td>{currentData && currentData[year] && currentData[year].qj}</td>
                    <td>{currentData && currentData[year] && currentData[year].ms}</td>
                    <td>{currentData && currentData[year] && currentData[year].lxpzje}</td>
                    <td>{currentData && currentData[year] && currentData[year].sbrcch}</td>
                    <td>{currentData && currentData[year] && currentData[year].tjrc}</td>
                    <td />
                  </tr>
                );
              })}
            </tbody>
          </table>
        </Card>
      </PageHeaderWrapper>
    );
  }
}

export default InfoTable;
