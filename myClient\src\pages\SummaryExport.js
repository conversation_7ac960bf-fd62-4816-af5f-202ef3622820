/* eslint-disable consistent-return */
/* eslint-disable camelcase */
import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Card, Tabs, Row, Col, Checkbox, Button, Divider, message } from 'antd';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';

import { downloadExport } from '@/utils/utils';
import { summary_years } from '../defaultSettings';

const { TabPane } = Tabs;
const CheckboxGroup = Checkbox.Group;

const baseItems = [
  ['num', '人员代码'],
  ['college', '所在单位'],
  ['enter_level', '人才层次'],
  ['name', '姓名'],
  ['sex', '性别'],
  ['political_outlook', '政治面貌'],
  ['birthday', '出生年月'],
  ['native_place', '籍贯'],
  ['enter_date', '入校/选/站时间'],
  ['title', '职称'],
  ['review_time', '评审时间'],
  ['Dr_school', '毕业学校'],
  ['Dr_major', '博士专业'],
  ['graduation_date', '博士毕业'],
  ['Dr_tutor_name', '博导姓名'],
  ['Dr_tutor_title', '博导称号'],
  ['overseas', '海外经历'],
  ['overseas_time', '经历时限'],
  ['overseas_Dr', '海外博士'],
  ['after_Dr', '博后经历'],
  ['settling_in_allowance', '安家费'],
  ['scientific_research_funds', '科研经费'],
  ['lab_construction_fee', '实验经费'],
  ['housing', '住房'],
  ['housing_subsidies', '购房补贴'],
  ['makeshift_house', '过渡住房'],
  ['scientific_research_house', '科研用房'],
  ['title_tp', '职称特评'],
  ['spouse_work', '配偶工作'],
  ['settling_in_allowance__r', '安家费（兑现）'],
  ['scientific_research_funds__r', '科研经费（兑现）'],
  ['lab_construction_fee__r', '实验室建设费（兑现）'],
  ['housing_subsidies__r', '购房补贴（兑现）'],
  ['housing__r', '住房（兑现）'],
  ['housing_date__r', '住房分配时间'],
  ['makeshift_house__r', '过渡住房（兑现）'],
  ['makeshift_house_date__r', '过渡住房分配时间'],
  ['scientific_research_house__r', '科研用房（兑现）'],
  ['srh_date__r', '科研用房分配时间'],
  ['spouse_work__r', '配偶工作（兑现）'],
  ['spouse_work_date__r', '配偶工作安排时间'],
  ['sum', '总数'],
  ['sci', 'SCI/C刊'],
  ['sci1', 'SCI1'],
  ['sci2', 'SCI2'],
  ['qj_date', '青基获批时限'],
  ['ms_date', '面上获批时限'],
  ['jfze', '项目经费总额'],
  ['sbrcch', '申报人才称号'],
  ['tjrc', '推荐人才'],
  ['hjqk', '获奖'],
];

const thesisItems = [
  ['num', '人员代码'],
  ['user_id.name', '姓名'],
  ['zzbx', '作者排序'],
  ['tj_year', '提交年度'],
  ['lx', '类型'],
  ['lwmc', '论文名称'],
  ['fbqk', '发表期刊'],
  ['zz', '作者'],
  ['fbsj', '发表时间'],
  ['j', '卷'],
  ['slqk', '收录情况'],
  ['q', '期'],
  ['issn', 'ISSN'],
  ['zeng_zheng', '增刊还是正刊'],
  ['yy', '语言'],
  ['yxyz', '影响因子'],
  ['remark', '备注'],
];

const projectItems = [
  ['num', '人员代码'],
  // ['ssxb', '所在单位'],
  ['user_id.name', '姓名'],
  ['xmlx', '项目类型'],
  ['lxnd', '立项年度'],
  ['xmxh', '项目序号'],
  ['xmmc', '项目名称'],
  ['xmbh', '项目编号'],
  ['xmzjly', '项目直接来源'],
  ['xmjb', '项目级别'],
  ['lxpzje', '立项批准金额'],
];

const otherItems = [
  ['num', '人员代码'],
  ['name', '姓名'],
  ['xmjf', '项目经费'],
  ['sbrcch', '申报人才称号'],
  ['tjrc', '推荐人才'],
  ['remark', '备注'],
];

/* eslint react/no-multi-comp:0 */
@connect(({ report, loading }) => ({
  report,
  loading: loading.models.report,
}))
class SummaryExport extends PureComponent {
  state = {
    baseCheckedList: [],
    baseIndeterminate: false,
    baseCheckAll: false,
    thesisCheckedList: [],
    thesisIndeterminate: false,
    thesisCheckAll: false,
    projectCheckedList: [],
    projectIndeterminate: false,
    projectCheckAll: false,
    otherCheckedList: [],
    otherIndeterminate: false,
    otherCheckAll: false,
  };

  baseOnChange = baseCheckedList => {
    this.setState({
      baseCheckedList,
      baseIndeterminate: !!baseCheckedList.length && baseCheckedList.length < baseItems.length,
      baseCheckAll: baseCheckedList.length === baseItems.length,
    });
  };

  baseOnCheckAllChange = e => {
    this.setState({
      baseCheckedList: e.target.checked ? baseItems.map(item => item[0]) : [],
      baseIndeterminate: false,
      baseCheckAll: e.target.checked,
    });
  };

  thesisOnChange = thesisCheckedList => {
    this.setState({
      thesisCheckedList,
      thesisIndeterminate:
        !!thesisCheckedList.length && thesisCheckedList.length < thesisItems.length,
      thesisCheckAll: thesisCheckedList.length === thesisItems.length,
    });
  };

  thesisOnCheckAllChange = e => {
    this.setState({
      thesisCheckedList: e.target.checked ? thesisItems.map(item => item[0]) : [],
      thesisIndeterminate: false,
      thesisCheckAll: e.target.checked,
    });
  };

  projectOnChange = projectCheckedList => {
    this.setState({
      projectCheckedList,
      projectIndeterminate:
        !!projectCheckedList.length && projectCheckedList.length < projectItems.length,
      projectCheckAll: projectCheckedList.length === projectItems.length,
    });
  };

  projectOnCheckAllChange = e => {
    this.setState({
      projectCheckedList: e.target.checked ? projectItems.map(item => item[0]) : [],
      projectIndeterminate: false,
      projectCheckAll: e.target.checked,
    });
  };

  otherOnChange = otherCheckedList => {
    this.setState({
      otherCheckedList,
      otherIndeterminate: !!otherCheckedList.length && otherCheckedList.length < otherItems.length,
      otherCheckAll: otherCheckedList.length === otherItems.length,
    });
  };

  otherOnCheckAllChange = e => {
    this.setState({
      otherCheckedList: e.target.checked ? otherItems.map(item => item[0]) : [],
      otherIndeterminate: false,
      otherCheckAll: e.target.checked,
    });
  };

  exportExcel = type => {
    const { dispatch } = this.props;
    const { baseCheckedList, thesisCheckedList, projectCheckedList, otherCheckedList } = this.state;

    let cols = [];
    switch (type) {
      case 'treatment':
        cols = baseCheckedList;
        break;
      case 'thesis':
        cols = thesisCheckedList;
        break;
      case 'project':
        cols = projectCheckedList;
        break;
      case 'other':
        cols = otherCheckedList;
        break;
      default:
    }

    if (!cols.length) {
      message.warn('请选择下载内容。');
      return false;
    }

    message.success('下载中...');
    dispatch({
      type: 'report/exportToExcel',
      payload: {
        type,
        cols,
        years: summary_years,
      },
      callback: result => {
        if (result.status === 'ok') {
          message.success('开始下载...');
          downloadExport(result.fileName);
        } else {
          message.error(result.message);
        }
      },
    });
  };

  render() {
    const {
      baseIndeterminate,
      baseCheckAll,
      baseCheckedList,
      thesisIndeterminate,
      thesisCheckAll,
      thesisCheckedList,
      projectIndeterminate,
      projectCheckAll,
      projectCheckedList,
      otherIndeterminate,
      otherCheckAll,
      otherCheckedList,
    } = this.state;
    return (
      <PageHeaderWrapper title="汇总导出">
        <Card bordered={false}>
          <Tabs defaultActiveKey="1">
            <TabPane tab={<span>人员基本信息</span>} key="1">
              <Checkbox
                indeterminate={baseIndeterminate}
                onChange={this.baseOnCheckAllChange}
                checked={baseCheckAll}
              >
                全选
              </Checkbox>
              <Button
                type="link"
                icon="download"
                style={{
                  float: 'right',
                  marginTop: '-3px',
                  marginRight: '1em',
                }}
                onClick={() => {
                  this.exportExcel('treatment');
                }}
              >
                下载
              </Button>
              <Divider />
              <CheckboxGroup
                style={{ width: '100%' }}
                value={baseCheckedList}
                onChange={this.baseOnChange}
              >
                <Row>
                  {baseItems.map(item => (
                    <Col
                      key={item[0]}
                      xs={12}
                      sm={8}
                      md={8}
                      lg={6}
                      xl={4}
                      style={{ margin: '.5rem 0' }}
                    >
                      <Checkbox value={item[0]}>{item[1]}</Checkbox>
                    </Col>
                  ))}
                </Row>
              </CheckboxGroup>
            </TabPane>
            <TabPane tab={<span>论文</span>} key="2">
              <Checkbox
                indeterminate={thesisIndeterminate}
                onChange={this.thesisOnCheckAllChange}
                checked={thesisCheckAll}
              >
                全选
              </Checkbox>
              <Button
                type="link"
                icon="download"
                style={{
                  float: 'right',
                  marginTop: '-3px',
                  marginRight: '1em',
                }}
                onClick={() => {
                  this.exportExcel('thesis');
                }}
              >
                下载
              </Button>
              <Divider />
              <CheckboxGroup
                style={{ width: '100%' }}
                value={thesisCheckedList}
                onChange={this.thesisOnChange}
              >
                <Row>
                  {thesisItems.map(item => (
                    <Col
                      key={item[0]}
                      xs={12}
                      sm={8}
                      md={8}
                      lg={6}
                      xl={4}
                      style={{ margin: '.5rem 0' }}
                    >
                      <Checkbox value={item[0]}>{item[1]}</Checkbox>
                    </Col>
                  ))}
                </Row>
              </CheckboxGroup>
            </TabPane>
            <TabPane tab={<span>项目</span>} key="3">
              <Checkbox
                indeterminate={projectIndeterminate}
                onChange={this.projectOnCheckAllChange}
                checked={projectCheckAll}
              >
                全选
              </Checkbox>
              <Button
                type="link"
                icon="download"
                style={{
                  float: 'right',
                  marginTop: '-3px',
                  marginRight: '1em',
                }}
                onClick={() => {
                  this.exportExcel('project');
                }}
              >
                下载
              </Button>
              <Divider />
              <CheckboxGroup
                style={{ width: '100%' }}
                value={projectCheckedList}
                onChange={this.projectOnChange}
              >
                <Row>
                  {projectItems.map(item => (
                    <Col
                      key={item[0]}
                      xs={12}
                      sm={8}
                      md={8}
                      lg={6}
                      xl={4}
                      style={{ margin: '.5rem 0' }}
                    >
                      <Checkbox value={item[0]}>{item[1]}</Checkbox>
                    </Col>
                  ))}
                </Row>
              </CheckboxGroup>
            </TabPane>
            <TabPane tab={<span>其他</span>} key="4">
              <Checkbox
                indeterminate={otherIndeterminate}
                onChange={this.otherOnCheckAllChange}
                checked={otherCheckAll}
              >
                全选
              </Checkbox>
              <Button
                type="link"
                icon="download"
                style={{
                  float: 'right',
                  marginTop: '-3px',
                  marginRight: '1em',
                }}
                onClick={() => {
                  this.exportExcel('other');
                }}
              >
                下载
              </Button>
              <Divider />
              <CheckboxGroup
                style={{ width: '100%' }}
                value={otherCheckedList}
                onChange={this.otherOnChange}
              >
                <Row>
                  {otherItems.map(item => (
                    <Col
                      key={item[0]}
                      xs={12}
                      sm={8}
                      md={8}
                      lg={6}
                      xl={4}
                      style={{ margin: '.5rem 0' }}
                    >
                      <Checkbox value={item[0]}>{item[1]}</Checkbox>
                    </Col>
                  ))}
                </Row>
              </CheckboxGroup>
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    );
  }
}

export default SummaryExport;
