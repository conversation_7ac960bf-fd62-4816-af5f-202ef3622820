import { stringify } from 'qs';
import request from '@/utils/request';

/**
 * 查询列表
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function query(params) {
  // return request(`${SERVER_HOST[API_ENV]}/ot_r_list?${stringify(params)}`);
  return request(`/server/api/ot_r_list?${stringify(params)}`);
}

/**
 * 创建
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function create(params) {
  // return request(`${SERVER_HOST[API_ENV]}/createOt_r`, {
  return request(`/server/api/createOt_r`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function update(params) {
  // return request(`${SERVER_HOST[API_ENV]}/updateOt_r`, {
  return request(`/server/api/updateOt_r`, {
    method: 'POST',
    data: params,
  });
}

/**
 * 批量删除
 *
 * @export
 * @param {*} params
 * @returns
 */
export async function removeList(params) {
  // return request(`${SERVER_HOST[API_ENV]}/delOt_r_list`, {
  return request(`/server/api/delOt_r_list`, {
    method: 'POST',
    data: params,
  });
}
