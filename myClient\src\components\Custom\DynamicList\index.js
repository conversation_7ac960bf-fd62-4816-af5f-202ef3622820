/* eslint-disable no-console */
/* eslint-disable react/no-string-refs */
import React, { PureComponent } from 'react';
import { Row, Col, Input, DatePicker, Icon } from 'antd';
import moment from 'moment';

import styles from './index.less';

const yearFormat = 'YYYY';

export default class DynamicList extends PureComponent {
  constructor(props) {
    super(props);
    const { array, canEdit, addonAfter } = props;
    this.state = {
      array: array || [],
      canEdit,
      addonAfter,
      openDatePickers: [],
    };
  }

  getValue = () => {
    const { array } = this.state;
    return array;
  };

  onChange = (value, index) => {
    const { array } = this.state;
    const ind = index.split(',');
    const newArr = array.map(arr => arr);
    newArr[ind[0]][ind[1]] = value;
    this.setState({
      array: newArr,
    });

    const { handlerUpdateValue } = this.props;
    if (typeof handlerUpdateValue === 'function') {
      handlerUpdateValue(array);
    }
  };

  addOne = () => {
    const { array } = this.state;
    this.setState({
      array: array.concat([[null, '']]),
    });
  };

  deleteOne = e => {
    const { array } = this.state;
    const index = e.currentTarget.getAttribute('index');
    const newArr = [];
    for (let i = 0; i < array.length; i += 1) {
      const arr = array[i];
      if (i !== parseInt(index, 10)) {
        newArr.push(arr);
      }
    }
    // newArr.splice(index, 1);
    console.dir(newArr);
    this.setState({
      array: newArr,
    });
  };

  emptyRow = addonAfter => (
    <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
      <Col span={12}>
        <Input disabled addonAfter={addonAfter} />
      </Col>
      <Col span={12}>
        <DatePicker mode="year" format={yearFormat} disabled />
      </Col>
    </Row>
  );

  viewRow = (time, val, index, addonAfter) => (
    <Row key={index} gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
      <Col span={12}>
        <Input disabled value={val} addonAfter={addonAfter} />
      </Col>
      <Col span={12}>
        <DatePicker
          mode="year"
          format={yearFormat}
          disabled
          value={!time || time === '' ? null : moment(time, yearFormat)}
        />
      </Col>
    </Row>
  );

  editRow = (time, val, index, addonAfter) => {
    const self = this;
    const { openDatePickers } = this.state;
    return (
      <Row className="editRow" key={index} gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
        <Col span={12}>
          <Input
            className="value"
            value={val}
            addonAfter={addonAfter}
            onChange={e => {
              this.onChange(e.target.value, `${index},1`);
            }}
          />
        </Col>
        <Col span={12}>
          <DatePicker
            mode="year"
            format={yearFormat}
            className="time"
            open={openDatePickers.includes(index)}
            value={!time || time === '' ? null : moment(time, yearFormat)}
            onFocus={() => {
              console.log('onFocus...');
              const arr = openDatePickers.map(v => v);
              if (!arr.includes(index)) arr.push(index);
              self.setState({ openDatePickers: arr });
            }}
            onPanelChange={v => {
              console.log('onPanelChange...');
              self.onChange(v.year(), `${index},0`);
              self.setState({ openDatePickers: [] });
            }}
          />
          <a
            title="删除"
            index={index}
            onClick={this.deleteOne.bind(this)}
            style={{ marginLeft: '.5rem' }}
          >
            <Icon type="minus-circle" theme="filled" style={{ color: 'red' }} />
          </a>
        </Col>
      </Row>
    );
  };

  render() {
    const { array, canEdit, addonAfter } = this.state;

    if (canEdit) {
      return (
        <div ref="root" className={styles.root}>
          {!!array && !!array.length
            ? array.map((arr, index) => this.editRow(arr[0], arr[1], index, addonAfter))
            : ''}
          <a onClick={this.addOne.bind(this)}>增加</a>
        </div>
      );
    }
    return (
      <div className={styles.root}>
        {!!array && !!array.length
          ? array.map((arr, index) => this.viewRow(arr[0], arr[1], index, addonAfter))
          : this.emptyRow(addonAfter)}
      </div>
    );
  }
}
