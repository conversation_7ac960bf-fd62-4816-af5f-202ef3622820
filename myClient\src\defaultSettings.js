module.exports = {
  navTheme: 'dark', // theme for nav menu
  primaryColor: '#1890FF', // primary color of ant design
  layout: 'sidemenu', // nav menu position: sidemenu or topmenu
  contentWidth: 'Fluid', // layout of content: Fluid or Fixed, only works when layout is topmenu
  fixedHeader: false, // sticky header
  autoHideHeader: false, // auto hide header
  fixSiderbar: false, // sticky siderbar
  menu: {
    disableLocal: false,
  },
  title: '西安科技大学高层次人才服务平台',
  shortTitle: '人才服务平台',
  summary_years: (() => {
    // 统计年份范围，前5年
    const summary = 5;
    const currentYear = new Date().getFullYear();
    const list = [];
    for (let i = 0; i <= summary; i += 1) {
      list.push(`${parseInt(currentYear, 10) - (summary - i)}`);
    }
    return list;
  })(),
  pwa: true,
  // Your custom iconfont Symbol script Url
  // eg：//at.alicdn.com/t/font_1039637_btcrd5co4w.js
  // 注意：如果需要图标多色，Iconfont 图标项目里要进行批量去色处理
  // Usage: https://github.com/ant-design/ant-design-pro/pull/3517
  iconfontUrl: '',
};
