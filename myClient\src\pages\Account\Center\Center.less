@import '~antd/lib/style/themes/default.less';
@import '~@/utils/utils.less';

.avatarHolder {
  margin-bottom: 24px;
  text-align: center;

  & > img {
    width: 104px;
    height: 104px;
    margin-bottom: 20px;
  }

  .name {
    margin-bottom: 4px;
    color: @heading-color;
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
  }
}

.detail {
  p {
    position: relative;
    margin-bottom: 8px;
    padding-left: 26px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  i {
    position: absolute;
    top: 4px;
    left: 0;
    width: 14px;
    height: 14px;
    background: url(https://gw.alipayobjects.com/zos/rmsportal/pBjWzVAHnOOtAUvZmZfy.svg);

    &.title {
      background-position: 0 0;
    }

    &.group {
      background-position: 0 -22px;
    }

    &.address {
      background-position: 0 -44px;
    }
  }
}

.tagsTitle,
.teamTitle {
  margin-bottom: 12px;
  color: @heading-color;
  font-weight: 500;
}

.tags {
  :global {
    .ant-tag {
      margin-bottom: 8px;
    }
  }
}

.team {
  :global {
    .ant-avatar {
      margin-right: 12px;
    }
  }

  a {
    display: block;
    margin-bottom: 24px;
    color: @text-color;
    transition: color 0.3s;
    .textOverflow();

    &:hover {
      color: @primary-color;
    }
  }
}

.tabsCard {
  :global {
    .ant-card-head {
      padding: 0 16px;
    }
  }
}
