@import '~antd/lib/style/themes/default.less';
@import '~@/utils/utils.less';

.tableList {
  .tableListOperator {
    margin-bottom: 16px;
    button {
      margin-right: 8px;
    }
  }
  .tableListTable {
    :global {
      .ant-table {
        width: 100%;
        overflow-x: auto;
        th,
        td {
          white-space: pre;
        }
      }
      .ant-pagination {
        width: 100%;
        text-align: right;
        .ant-pagination-total-text {
          float: left;
        }
      }
    }
  }
}

.updateForm {
  :global {
    .ant-form-item {
      margin-bottom: 5px;
    }
    .ant-calendar-picker {
      width: 100%;
    }
    .ant-upload-list {
      display: inline-block;
      width: calc(100% - 120px);
      vertical-align: top;
      .ant-upload-list-item {
        margin-top: 5px;
      }
    }
    .ant-upload-disabled + .ant-upload-list {
      .anticon-close {
        display: none;
      }
    }
  }
}

.tableListForm {
  text-align: right;
  :global {
    .ant-form-item {
      margin-bottom: 24px;
      > .ant-form-item-label {
        width: auto;
        padding-right: 8px;
        line-height: 32px;
      }
      .ant-form-item-control {
        line-height: 32px;
      }
    }
    .ant-form-item-control-wrapper {
      flex: 1;
    }
  }
  .submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }
}

@media screen and (max-width: @screen-lg) {
  .tableListForm :global(.ant-form-item) {
    margin-right: 24px;
  }
}

@media screen and (max-width: @screen-md) {
  .tableListForm :global(.ant-form-item) {
    margin-right: 8px;
  }
}
