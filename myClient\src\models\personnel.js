/* eslint-disable camelcase */
import userService from '@/services/user';
import treatment_t from '@/services/treatment_t';
import thesis_t from '@/services/thesis_t';
import project_t from '@/services/project_t';
import other_t from '@/services/other_t';

export default {
  namespace: 'personnel',

  state: {
    data: {
      list: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
      },
    },
  },

  effects: {
    /* ---------------------------------------基础信息-------------------------------------------- */

    /**
     * 获取所有用户列表
     *
     * @param {*} { payload }
     * @param {*} { call, put }
     */
    *getList({ payload }, { call, put }) {
      const response = yield call(userService.query, payload);

      response.list = Array.isArray(response.list) ? response.list : [];

      yield put({
        type: 'save',
        payload: response,
      });
    },

    /**
     * 创建用户
     *
     * @param {*} { payload, callback }
     * @param {*} { call }
     */
    *createUser({ payload, callback }, { call }) {
      const response = yield call(userService.createUser, payload);

      callback(response);
    },

    /**
     * 更新用户
     *
     * @param {*} { payload, callback }
     * @param {*} { call }
     */
    *updateUser({ payload, callback }, { call }) {
      const response = yield call(userService.updateUser, payload);

      callback(response);
    },

    /**
     * 批量删除用户
     *
     * @param {*} { payload, callback }
     * @param {*} { call }
     */
    *removeList({ payload, callback }, { call }) {
      const response = yield call(userService.removeList, payload);

      callback(response);
    },

    /* ---------------------------------------待遇-------------------------------------------- */

    /**
     * 新建待遇协议
     *
     * @param {*} { payload, callback }
     * @param {*} { call }
     */
    *createTr_t({ payload, callback }, { call }) {
      const response = yield call(treatment_t.create, payload);

      callback(response);
    },

    /**
     * 查询待遇协议
     *
     * @param {*} { payload, callback }
     * @param {*} { call, put }
     */
    *queryTr_t({ payload, callback }, { call }) {
      const response = yield call(treatment_t.query, payload);

      if (response.status === 'ok') {
        const dataSource =
          Array.isArray(response.list) && response.list.length ? response.list[0] : {};
        callback(dataSource);
      }
    },

    /**
     * 更新待遇协议
     *
     * @param {*} { payload, callback }
     * @param {*} { call, put }
     */
    *updateTr_t({ payload, callback }, { call }) {
      const response = yield call(treatment_t.update, payload);

      callback(response);
    },

    /**
     * 删除待遇协议
     *
     * @param {*} { payload, callback }
     * @param {*} { call, put }
     */
    *removeTr_t({ payload, callback }, { call }) {
      const response = yield call(treatment_t.removeList, payload);

      callback(response);
    },

    /* ---------------------------------------论文-------------------------------------------- */

    /**
     * 新建论文目标
     *
     * @param {*} { payload, callback }
     * @param {*} { call }
     */
    *createTh_t({ payload, callback }, { call }) {
      const response = yield call(thesis_t.create, payload);

      callback(response);
    },

    /**
     * 查询论文目标
     *
     * @param {*} { payload, callback }
     * @param {*} { call, put }
     */
    *queryTh_t({ payload, callback }, { call }) {
      const response = yield call(thesis_t.query, payload);

      if (response.status === 'ok') {
        const dataSource =
          Array.isArray(response.list) && response.list.length ? response.list[0] : {};
        callback(dataSource);
      }
    },

    /**
     * 更新论文目标
     *
     * @param {*} { payload, callback }
     * @param {*} { call, put }
     */
    *updateTh_t({ payload, callback }, { call }) {
      const response = yield call(thesis_t.update, payload);

      callback(response);
    },

    /**
     * 删除论文目标
     *
     * @param {*} { payload, callback }
     * @param {*} { call, put }
     */
    *removeTh_t({ payload, callback }, { call }) {
      const response = yield call(thesis_t.removeList, payload);

      callback(response);
    },

    /* ---------------------------------------项目-------------------------------------------- */

    /**
     * 新建项目目标
     *
     * @param {*} { payload, callback }
     * @param {*} { call }
     */
    *createPr_t({ payload, callback }, { call }) {
      const response = yield call(project_t.create, payload);

      callback(response);
    },

    /**
     * 查询项目目标
     *
     * @param {*} { payload, callback }
     * @param {*} { call, put }
     */
    *queryPr_t({ payload, callback }, { call }) {
      const response = yield call(project_t.query, payload);

      if (response.status === 'ok') {
        const dataSource =
          Array.isArray(response.list) && response.list.length ? response.list[0] : {};
        callback(dataSource);
      }
    },

    /**
     * 更新项目目标
     *
     * @param {*} { payload, callback }
     * @param {*} { call, put }
     */
    *updatePr_t({ payload, callback }, { call }) {
      const response = yield call(project_t.update, payload);

      callback(response);
    },

    /**
     * 删除项目目标
     *
     * @param {*} { payload, callback }
     * @param {*} { call, put }
     */
    *removePr_t({ payload, callback }, { call }) {
      const response = yield call(project_t.removeList, payload);

      callback(response);
    },

    /* ---------------------------------------其他-------------------------------------------- */

    /**
     * 新建其他目标
     *
     * @param {*} { payload, callback }
     * @param {*} { call }
     */
    *createOt_t({ payload, callback }, { call }) {
      const response = yield call(other_t.create, payload);

      callback(response);
    },

    /**
     * 查询其他目标
     *
     * @param {*} { payload, callback }
     * @param {*} { call, put }
     */
    *queryOt_t({ payload, callback }, { call }) {
      const response = yield call(other_t.query, payload);

      if (response.status === 'ok') {
        const dataSource =
          Array.isArray(response.list) && response.list.length ? response.list[0] : {};
        callback(dataSource);
      }
    },

    /**
     * 更新其他目标
     *
     * @param {*} { payload, callback }
     * @param {*} { call, put }
     */
    *updateOt_t({ payload, callback }, { call }) {
      const response = yield call(other_t.update, payload);

      callback(response);
    },

    /**
     * 删除其他目标
     *
     * @param {*} { payload, callback }
     * @param {*} { call, put }
     */
    *removeOt_t({ payload, callback }, { call }) {
      const response = yield call(other_t.removeList, payload);

      callback(response);
    },
  },

  reducers: {
    save(state, action) {
      const { payload } = action;
      if (payload && payload.list && payload.list.length) {
        payload.pagination.showTotal = total => `共 ${total} 条记录`;
      }
      return {
        ...state,
        data: payload,
      };
    },
  },
};
