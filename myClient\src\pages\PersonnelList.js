/* eslint-disable camelcase */
import React, { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import router from 'umi/router';
import moment from 'moment';
import {
  Card,
  Row,
  Col,
  Form,
  Input,
  Select,
  Radio,
  DatePicker,
  Button,
  Divider,
  Modal,
  Tabs,
  message,
} from 'antd';
import StandardTable from '@/components/StandardTable';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';

import styles from './PersonnelList.less';

const FormItem = Form.Item;
const { TabPane } = Tabs;
const { Option } = Select;
const RadioGroup = Radio.Group;
const { MonthPicker } = DatePicker;

const dateFormat = 'YYYY/MM/DD';
const monthFormat = 'YYYY/MM';

const { confirm } = Modal;
/**
 * 全局方法，弹出确认窗口
 *
 * @param {String} title 标题
 * @param {String} content 描述
 * @param {Function} onOk 确认回调
 * @param {Function} onCancel 取消回调
 */
function showConfirm(title, content, onOk, onCancel) {
  confirm({
    title,
    content,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      if (typeof onOk === 'function') onOk();
    },
    onCancel: () => {
      if (typeof onCancel === 'function') onCancel();
    },
  });
}

@Form.create()
class UpdateForm extends PureComponent {
  formLayout = {
    layout: 'inline',
    labelCol: {
      xs: { span: 24 },
      sm: { span: 10 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 14 },
    },
  };

  formItemLayout = {
    style: {
      width: '100%',
    },
  };

  /**
   * 基础信息表单
   *
   * @returns
   * @memberof UpdateForm
   */
  baseInfoForm() {
    const {
      form: { getFieldDecorator },
      form,
      userLevel,
      values = {},
      infoMode,
      handleUpdateModalUserlevel,
      handleSaveBaseInfo,
    } = this.props;

    const hasBaseValue = infoMode === 'edit' || infoMode === 'view';
    const canBaseEdit = infoMode === 'create' || infoMode === 'edit';

    return (
      <Form
        {...this.formLayout}
        onSubmit={e => {
          e.preventDefault();
          handleSaveBaseInfo(form, infoMode);
        }}
      >
        <Card>
          <Row>
            <Col span={12}>
              <FormItem {...this.formItemLayout} label="人员代码" required>
                {getFieldDecorator('num', {
                  initialValue: hasBaseValue ? values.num : '',
                  rules: [{ required: true, message: '不能为空！' }],
                })(<Input disabled={hasBaseValue} />)}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem {...this.formItemLayout} label="账号密码">
                {getFieldDecorator('pwd', { initialValue: hasBaseValue ? values.pwd : '' })(
                  <Input placeholder="默认密码为xkd1234" disabled={!canBaseEdit} />
                )}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem {...this.formItemLayout} label="姓名" required>
                {getFieldDecorator('name', {
                  initialValue: hasBaseValue ? values.name : '',
                  rules: [{ required: true, message: '不能为空！' }],
                })(<Input disabled={!canBaseEdit} />)}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem {...this.formItemLayout} label="性别" required>
                {getFieldDecorator('sex', { initialValue: hasBaseValue ? values.sex : '男' })(
                  <Select disabled={!canBaseEdit}>
                    <Option value="男">男</Option>
                    <Option value="女">女</Option>
                  </Select>
                )}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <FormItem
                {...this.formItemLayout}
                label="权限"
                required
                labelCol={{
                  xs: { span: 24 },
                  sm: { span: 4 },
                }}
                wrapperCol={{
                  xs: { span: 24 },
                  sm: { span: 20 },
                }}
              >
                {getFieldDecorator('level', { initialValue: hasBaseValue ? values.level : 1 })(
                  <RadioGroup
                    disabled={!canBaseEdit}
                    onChange={e => {
                      handleUpdateModalUserlevel(e.target.value);
                    }}
                  >
                    <Radio value={0}>管理员</Radio>
                    <Radio value={1}>普通用户</Radio>
                  </RadioGroup>
                )}
              </FormItem>
            </Col>
          </Row>
        </Card>
        {userLevel === 1 ? (
          <Card>
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="入校层次">
                  {getFieldDecorator('enter_level', {
                    initialValue: hasBaseValue ? values.enter_level : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="入校/选/站时间">
                  {getFieldDecorator('enter_date', {
                    initialValue:
                      hasBaseValue && values.enter_date
                        ? moment(new Date(values.enter_date), monthFormat)
                        : null,
                  })(<MonthPicker format={monthFormat} disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="所在单位">
                  {getFieldDecorator('college', {
                    initialValue: hasBaseValue ? values.college : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="政治面貌">
                  {getFieldDecorator('political_outlook', {
                    initialValue: hasBaseValue ? values.political_outlook : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="出生年月">
                  {getFieldDecorator('birthday', {
                    initialValue:
                      hasBaseValue && values.birthday
                        ? moment(new Date(values.birthday), monthFormat)
                        : null,
                  })(<MonthPicker format={monthFormat} disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="籍贯">
                  {getFieldDecorator('native_place', {
                    initialValue: hasBaseValue ? values.native_place : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="联系方式">
                  {getFieldDecorator('mobile', {
                    initialValue: hasBaseValue ? values.mobile : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="人员状态">
                  {getFieldDecorator('status', {
                    initialValue: hasBaseValue ? values.status : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="博士专业">
                  {getFieldDecorator('Dr_major', {
                    initialValue: hasBaseValue ? values.Dr_major : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="签约时间">
                  {getFieldDecorator('signing_time', {
                    initialValue:
                      hasBaseValue && values.signing_time
                        ? moment(new Date(values.signing_time), dateFormat)
                        : null,
                  })(<DatePicker format={dateFormat} disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="职称">
                  {getFieldDecorator('title', { initialValue: hasBaseValue ? values.title : '' })(
                    <Input disabled={!canBaseEdit} />
                  )}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="评审时间">
                  {getFieldDecorator('review_time', {
                    initialValue:
                      hasBaseValue && values.review_time
                        ? moment(new Date(values.review_time), monthFormat)
                        : null,
                  })(<MonthPicker format={monthFormat} disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="毕业学校">
                  {getFieldDecorator('Dr_school', {
                    initialValue: hasBaseValue ? values.Dr_school : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="毕业时间">
                  {getFieldDecorator('graduation_date', {
                    initialValue:
                      hasBaseValue && values.graduation_date
                        ? moment(new Date(values.graduation_date), monthFormat)
                        : null,
                  })(<MonthPicker format={monthFormat} disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="博导姓名">
                  {getFieldDecorator('Dr_tutor_name', {
                    initialValue: hasBaseValue ? values.Dr_tutor_name : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="博导称号">
                  {getFieldDecorator('Dr_tutor_title', {
                    initialValue: hasBaseValue ? values.Dr_tutor_title : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="海外经历">
                  {getFieldDecorator('overseas', {
                    initialValue: hasBaseValue ? values.overseas : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="经历时限">
                  {getFieldDecorator('overseas_time', {
                    initialValue: hasBaseValue ? values.overseas_time : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="海外博士">
                  {getFieldDecorator('overseas_Dr', {
                    initialValue: hasBaseValue ? values.overseas_Dr : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="博后经历">
                  {getFieldDecorator('after_Dr', {
                    initialValue: hasBaseValue ? values.after_Dr : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
          </Card>
        ) : (
          ''
        )}

        <Row>
          <Col span={24} style={{ textAlign: 'right' }}>
            {canBaseEdit ? (
              <Button type="primary" style={{ margin: '1em', width: '10em' }} htmlType="submit">
                保存
              </Button>
            ) : (
              ''
            )}
          </Col>
        </Row>
      </Form>
    );
  }

  /**
   * 待遇协议表单
   *
   * @returns
   * @memberof UpdateForm
   */
  treatmentTargetForm() {
    const {
      form: { getFieldDecorator },
      form,
      treatment_t = {},
      infoMode,
      handleSaveTr_tInfo,
    } = this.props;

    const hasBaseValue = infoMode === 'edit' || infoMode === 'view';
    const canBaseEdit = infoMode === 'create' || infoMode === 'edit';

    return (
      <Form
        {...this.formLayout}
        onSubmit={e => {
          e.preventDefault();
          handleSaveTr_tInfo(form, infoMode);
        }}
      >
        <Card>
          <Row>
            <Col span={12}>
              <FormItem {...this.formItemLayout} label="安家费">
                {getFieldDecorator('settling_in_allowance', {
                  initialValue: hasBaseValue ? treatment_t.settling_in_allowance : '',
                })(<Input disabled={!canBaseEdit} addonAfter="万元" />)}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem {...this.formItemLayout} label="科研经费">
                {getFieldDecorator('scientific_research_funds', {
                  initialValue: hasBaseValue ? treatment_t.scientific_research_funds : '',
                })(<Input disabled={!canBaseEdit} addonAfter="万元" />)}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem {...this.formItemLayout} label="实验室建设">
                {getFieldDecorator('lab_construction_fee', {
                  initialValue: hasBaseValue ? treatment_t.lab_construction_fee : '',
                })(<Input disabled={!canBaseEdit} addonAfter="万元" />)}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem {...this.formItemLayout} label="住房">
                {getFieldDecorator('housing', {
                  initialValue: hasBaseValue ? treatment_t.housing : '',
                })(<Input disabled={!canBaseEdit} />)}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem {...this.formItemLayout} label="购房补贴">
                {getFieldDecorator('housing_subsidies', {
                  initialValue: hasBaseValue ? treatment_t.housing_subsidies : '',
                })(<Input disabled={!canBaseEdit} addonAfter="万元" />)}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem {...this.formItemLayout} label="过渡住房">
                {getFieldDecorator('makeshift_house', {
                  initialValue: hasBaseValue ? treatment_t.makeshift_house : '',
                })(<Input disabled={!canBaseEdit} />)}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem {...this.formItemLayout} label="科研用房">
                {getFieldDecorator('scientific_research_house', {
                  initialValue: hasBaseValue ? treatment_t.scientific_research_house : '',
                })(<Input disabled={!canBaseEdit} />)}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem {...this.formItemLayout} label="职称特评">
                {getFieldDecorator('title_tp', {
                  initialValue: hasBaseValue ? treatment_t.title_tp : '',
                })(<Input disabled={!canBaseEdit} />)}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem {...this.formItemLayout} label="配偶工作">
                {getFieldDecorator('spouse_work', {
                  initialValue: hasBaseValue ? treatment_t.spouse_work : '',
                })(<Input disabled={!canBaseEdit} />)}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <FormItem
                {...this.formItemLayout}
                labelCol={{
                  xs: { span: 24 },
                  sm: { span: 4 },
                }}
                wrapperCol={{
                  xs: { span: 24 },
                  sm: { span: 20 },
                }}
                label="备注"
              >
                {getFieldDecorator('remark', {
                  initialValue: hasBaseValue ? treatment_t.remark : '',
                })(<Input disabled={!canBaseEdit} />)}
              </FormItem>
            </Col>
          </Row>
        </Card>
        <Row>
          <Col span={24} style={{ textAlign: 'right' }}>
            {canBaseEdit ? (
              <Button type="primary" style={{ margin: '1em', width: '10em' }} htmlType="submit">
                保存
              </Button>
            ) : (
              ''
            )}
          </Col>
        </Row>
      </Form>
    );
  }

  /**
   * 目标表单
   *
   * @returns
   * @memberof UpdateForm
   */
  targetForm() {
    const {
      form: { getFieldDecorator },
      form,
      infoMode,
      thesis_t = {},
      project_t = {},
      other_t = {},
      handleSaveTh_tInfo,
      handleSavePr_tInfo,
      handleSaveOt_tInfo,
    } = this.props;

    const hasBaseValue = infoMode === 'edit' || infoMode === 'view';
    const canBaseEdit = infoMode === 'create' || infoMode === 'edit';

    return (
      <div className={styles.targetFormList}>
        <Form
          {...this.formLayout}
          onSubmit={e => {
            e.preventDefault();
            handleSaveTh_tInfo(form, infoMode);
          }}
        >
          <Card title="论文目标">
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="总数">
                  {getFieldDecorator('sum', {
                    initialValue: hasBaseValue ? thesis_t.sum : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="SCI/C刊">
                  {getFieldDecorator('sci', {
                    initialValue: hasBaseValue ? thesis_t.sci : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="SCI1">
                  {getFieldDecorator('sci1', {
                    initialValue: hasBaseValue ? thesis_t.sci1 : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="SCI2">
                  {getFieldDecorator('sci2', {
                    initialValue: hasBaseValue ? thesis_t.sci2 : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={24}>
                <FormItem
                  {...this.formItemLayout}
                  labelCol={{
                    xs: { span: 24 },
                    sm: { span: 4 },
                  }}
                  wrapperCol={{
                    xs: { span: 24 },
                    sm: { span: 20 },
                  }}
                  label="备注"
                >
                  {getFieldDecorator('remark', {
                    initialValue: hasBaseValue ? thesis_t.remark : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={24} style={{ textAlign: 'right' }}>
                {canBaseEdit ? (
                  <Button type="primary" style={{ margin: '1em', width: '10em' }} htmlType="submit">
                    保存
                  </Button>
                ) : (
                  ''
                )}
              </Col>
            </Row>
          </Card>
        </Form>
        <Form
          {...this.formLayout}
          onSubmit={e => {
            e.preventDefault();
            handleSavePr_tInfo(form, infoMode);
          }}
        >
          <Card title="项目目标">
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="青基获批时限">
                  {getFieldDecorator('qj_date', {
                    initialValue:
                      hasBaseValue && project_t.qj_date
                        ? moment(new Date(project_t.qj_date), monthFormat)
                        : null,
                  })(<MonthPicker format={monthFormat} disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="面上获批时限">
                  {getFieldDecorator('ms_date', {
                    initialValue:
                      hasBaseValue && project_t.ms_date
                        ? moment(new Date(project_t.ms_date), monthFormat)
                        : null,
                  })(<MonthPicker format={monthFormat} disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="项目经费">
                  {getFieldDecorator('jfze', {
                    initialValue: hasBaseValue ? project_t.jfze : '',
                  })(<Input disabled={!canBaseEdit} addonAfter="万元" />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="备注">
                  {getFieldDecorator('remark', {
                    initialValue: hasBaseValue ? project_t.remark : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={24} style={{ textAlign: 'right' }}>
                {canBaseEdit ? (
                  <Button type="primary" style={{ margin: '1em', width: '10em' }} htmlType="submit">
                    保存
                  </Button>
                ) : (
                  ''
                )}
              </Col>
            </Row>
          </Card>
        </Form>
        <Form
          {...this.formLayout}
          onSubmit={e => {
            e.preventDefault();
            handleSaveOt_tInfo(form, infoMode);
          }}
        >
          <Card title="其他目标">
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="申报人才称号">
                  {getFieldDecorator('sbrcch', {
                    initialValue: hasBaseValue ? other_t.sbrcch : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="推荐人才">
                  {getFieldDecorator('tjrc', {
                    initialValue: hasBaseValue ? other_t.tjrc : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="获奖">
                  {getFieldDecorator('hjqk', {
                    initialValue: hasBaseValue ? other_t.hjqk : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem {...this.formItemLayout} label="备注">
                  {getFieldDecorator('remark', {
                    initialValue: hasBaseValue ? other_t.remark : '',
                  })(<Input disabled={!canBaseEdit} />)}
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={24} style={{ textAlign: 'right' }}>
                {canBaseEdit ? (
                  <Button type="primary" style={{ margin: '1em', width: '10em' }} htmlType="submit">
                    保存
                  </Button>
                ) : (
                  ''
                )}
              </Col>
            </Row>
          </Card>
        </Form>
      </div>
    );
  }

  render() {
    const { visible, title, values, userLevel, handleUpdateModalVisible } = this.props;

    return (
      <Modal
        width={640}
        bodyStyle={{ padding: '32px 40px 48px' }}
        destroyOnClose
        title={title}
        visible={visible}
        maskClosable={false}
        footer={null}
        onCancel={() => handleUpdateModalVisible(false, title, values)}
        // afterClose={() => handleUpdateModalVisible()}
      >
        <Tabs defaultActiveKey="1">
          <TabPane tab={<span>基础信息</span>} key="1">
            {this.baseInfoForm()}
          </TabPane>
          {userLevel
            ? ['待遇协议', '目标协议'].map((t, index) => {
                return (
                  <TabPane tab={<span>{t}</span>} key={`${index + 2}`}>
                    {t === '待遇协议' ? this.treatmentTargetForm() : this.targetForm()}
                  </TabPane>
                );
              })
            : ''}
        </Tabs>
      </Modal>
    );
  }
}

/* eslint react/no-multi-comp:0 */
@connect(({ personnel, loading }) => ({
  personnel,
  loading: loading.models.personnel,
}))
@Form.create()
class TableList extends PureComponent {
  state = {
    showListMode: 1, // 当前列表显示的用户分组， 0 管理员， 1 普通用户
    selectedRows: [], // 选中行
    updateModal: {
      // 用户信息窗口
      visible: false, // 显示状态
      userLevel: 1,
      title: '', // 标题
      values: {}, // 内容
      treatment_t: {}, // 当前选中用户的待遇协议信息
      thesis_t: {}, // 当前选中用户的论文目标
      project_t: {}, // 当前选中用户的项目目标
      other_t: {}, // 当前选中用户的其他目标
      infoMode: 'create', // 基础信息编辑状态, create|edit|view
    },
  };

  // 表列配置
  columns = [
    {
      title: '人员代码',
      dataIndex: 'num',
      // sorter: true,
      render: (text, record) => (
        <div>
          {record.level === 1 ? (
            <a
              onClick={() => {
                router.push(`/jbqkb?num=${text}`);
              }}
            >
              {text}
            </a>
          ) : (
            text
          )}
        </div>
      ),
    },
    {
      title: '姓名',
      dataIndex: 'name',
      // sorter: true,
    },
    {
      title: '所在单位',
      dataIndex: 'college',
    },
    {
      title: '人才层次',
      dataIndex: 'enter_level',
    },
    {
      title: '入校/选/站时间',
      dataIndex: 'enter_date',
      render: text => {
        if (text) {
          const time = new Date(text);
          return `${time.getFullYear()}年${time.getMonth() + 1}月`;
        }
        return '';
      },
    },
    {
      title: '职称',
      dataIndex: 'title',
    },
    {
      title: '评审时间',
      dataIndex: 'review_time',
      render: text => {
        if (text) {
          const time = new Date(text);
          return `${time.getFullYear()}年${time.getMonth() + 1}月`;
        }
        return '';
      },
    },
    {
      title: '操作',
      render: (text, record) => (
        <Fragment>
          <a onClick={() => this.handleUpdateModalVisible(true, '用户详情', record, 'view')}>
            查看
          </a>
          <Divider type="vertical" />
          <a onClick={() => this.handleUpdateModalVisible(true, '用户详情', record, 'edit')}>
            修改
          </a>
          {record.level === 1 && record.num !== 'admin' ? <Divider type="vertical" /> : ''}
          {record.level === 1 && record.num !== 'admin' ? (
            <a
              onClick={e => {
                e.preventDefault();
                this.handlerRemove([record.num]);
              }}
            >
              删除
            </a>
          ) : (
            ''
          )}
        </Fragment>
      ),
    },
  ];

  componentDidMount() {
    const {
      dispatch,
      personnel: {
        data: { pagination },
      },
    } = this.props;
    const { showListMode } = this.state;
    dispatch({
      type: 'personnel/getList',
      payload: {
        ...pagination,
        showListMode,
      },
    });
  }

  /**
   * 获取当前选中用户的待遇协议信息
   *
   * @param {Number} num 人员代码
   * @param {Function} callback 回调
   */
  getCurrentTr_t = (num, callback) => {
    const { dispatch } = this.props;

    dispatch({
      type: 'personnel/queryTr_t',
      payload: { num },
      callback,
    });
  };

  /**
   * 获取当前选中用户论文目标
   *
   * @param {Number} num 人员代码
   * @param {Function} callback 回调
   */
  getCurrentTh_t = (num, callback) => {
    const { dispatch } = this.props;

    dispatch({
      type: 'personnel/queryTh_t',
      payload: { num },
      callback,
    });
  };

  /**
   * 获取当前选中用户项目目标
   *
   * @param {Number} num 人员代码
   * @param {Function} callback 回调
   */
  getCurrentPr_t = (num, callback) => {
    const { dispatch } = this.props;

    dispatch({
      type: 'personnel/queryPr_t',
      payload: { num },
      callback,
    });
  };

  /**
   * 获取当前选中用户其他目标
   *
   * @param {Number} num 人员代码
   * @param {Function} callback 回调
   */
  getCurrentOt_t = (num, callback) => {
    const { dispatch } = this.props;

    dispatch({
      type: 'personnel/queryOt_t',
      payload: { num },
      callback,
    });
  };

  /**
   * 控制编辑用户信息组件显示状态
   *
   * @param {Boolean} flag 是否显示
   * @param {String} title 弹窗标题
   * @param {JSON} record 用户基础信息
   * @param {String} infoMode 弹窗模式， create|edit|view
   */
  handleUpdateModalVisible = (flag, title, record, infoMode) => {
    const updateModal = {
      ...this.state,
      visible: !!flag,
      userLevel: record ? record.level : 1,
      title,
      values: record || {},
      infoMode: infoMode || 'create',
    };

    if (updateModal.visible && updateModal.userLevel === 1 && updateModal.infoMode !== 'create') {
      // XXX: 实现方式待优化
      let tr = false;
      let th = false;
      let pr = false;
      let ot = false;
      this.getCurrentTr_t(record.num, doc => {
        updateModal.treatment_t = doc;
        tr = true;
        if (tr && th && pr && ot) {
          this.setState({
            updateModal,
          });
        }
      });
      this.getCurrentTh_t(record.num, doc => {
        updateModal.thesis_t = doc;
        th = true;
        if (tr && th && pr && ot) {
          this.setState({
            updateModal,
          });
        }
      });
      this.getCurrentPr_t(record.num, doc => {
        updateModal.project_t = doc;
        pr = true;
        if (tr && th && pr && ot) {
          this.setState({
            updateModal,
          });
        }
      });
      this.getCurrentOt_t(record.num, doc => {
        updateModal.other_t = doc;
        ot = true;
        if (tr && th && pr && ot) {
          this.setState({
            updateModal,
          });
        }
      });
    } else {
      updateModal.treatment_t = {};
      this.setState({
        updateModal,
      });
    }
  };

  /**
   * 改变弹出窗用户级别
   *
   * @param {*} level
   */
  handleUpdateModalUserlevel = level => {
    const { updateModal } = this.state;
    this.setState({
      updateModal: Object.assign({}, updateModal, { userLevel: level }),
    });
  };

  /**
   * 保存用户基础信息，自动判断新增是更新
   *
   * @param {Object} form 表单对象
   * @param {String} infoMode 提交模式，新增或更新
   */
  handleSaveBaseInfo = (form, infoMode) => {
    const {
      dispatch,
      personnel: {
        data: { pagination },
      },
    } = this.props;
    const { showListMode, updateModal } = this.state;

    if (form) {
      form.validateFields((err, fieldsValue) => {
        if (err) return;

        const values = {
          ...fieldsValue,
        };

        const saveUrl = infoMode === 'edit' ? 'personnel/updateUser' : 'personnel/createUser';
        dispatch({
          type: saveUrl,
          payload: values,
          callback: result => {
            if (result.status === 'ok') {
              message.success('保存成功');
              dispatch({
                type: 'personnel/getList',
                payload: {
                  ...pagination,
                  showListMode,
                },
              });
              if (infoMode === 'create') {
                this.setState({
                  updateModal: {
                    ...updateModal,
                    title: '用户详情',
                    values: result.result,
                    infoMode: 'edit',
                  },
                });
              }
            } else {
              message.error(result.message);
            }
          },
        });
      });
    }
  };

  /**
   * 保存待遇协议信息，自动判断新增是更新
   *
   * @param {Object} form 表单对象
   * @param {String} infoMode 提交模式，新增或更新
   */
  handleSaveTr_tInfo = (form, infoMode) => {
    const { dispatch } = this.props;

    if (form) {
      form.validateFields((err, fieldsValue) => {
        if (err) return;

        const values = {
          ...fieldsValue,
        };

        const saveUrl = infoMode === 'edit' ? 'personnel/updateTr_t' : 'personnel/createTr_t';
        dispatch({
          type: saveUrl,
          payload: values,
          callback: result => {
            if (result.status === 'ok') {
              message.success('保存成功');
            } else {
              message.error(result.message);
            }
          },
        });
      });
    }
  };

  /**
   * 保存论文目标信息，自动判断新增是更新
   *
   * @param {Object} form 表单对象
   * @param {String} infoMode 提交模式，新增或更新
   */
  handleSaveTh_tInfo = (form, infoMode) => {
    const { dispatch } = this.props;

    if (form) {
      form.validateFields((err, fieldsValue) => {
        if (err) return;

        const values = {
          ...fieldsValue,
        };

        const saveUrl = infoMode === 'edit' ? 'personnel/updateTh_t' : 'personnel/createTh_t';
        dispatch({
          type: saveUrl,
          payload: values,
          callback: result => {
            if (result.status === 'ok') {
              message.success('保存成功');
            } else {
              message.error(result.message);
            }
          },
        });
      });
    }
  };

  /**
   * 保存项目目标信息，自动判断新增是更新
   *
   * @param {Object} form 表单对象
   * @param {String} infoMode 提交模式，新增或更新
   */
  handleSavePr_tInfo = (form, infoMode) => {
    const { dispatch } = this.props;

    if (form) {
      form.validateFields((err, fieldsValue) => {
        if (err) return;

        const values = {
          ...fieldsValue,
        };

        const saveUrl = infoMode === 'edit' ? 'personnel/updatePr_t' : 'personnel/createPr_t';
        dispatch({
          type: saveUrl,
          payload: values,
          callback: result => {
            if (result.status === 'ok') {
              message.success('保存成功');
            } else {
              message.error(result.message);
            }
          },
        });
      });
    }
  };

  /**
   * 保存其他目标信息，自动判断新增是更新
   *
   * @param {Object} form 表单对象
   * @param {String} infoMode 提交模式，新增或更新
   */
  handleSaveOt_tInfo = (form, infoMode) => {
    const { dispatch } = this.props;

    if (form) {
      form.validateFields((err, fieldsValue) => {
        if (err) return;

        const values = {
          ...fieldsValue,
        };

        const saveUrl = infoMode === 'edit' ? 'personnel/updateOt_t' : 'personnel/createOt_t';
        dispatch({
          type: saveUrl,
          payload: values,
          callback: result => {
            if (result.status === 'ok') {
              message.success('保存成功');
            } else {
              message.error(result.message);
            }
          },
        });
      });
    }
  };

  /**
   * 选中行
   *
   * @param {Array} rows
   */
  handleSelectRows = rows => {
    this.setState({
      selectedRows: rows,
    });
  };

  /**
   * 模糊查询
   *
   * @param {Event} e
   */
  handleSearch = e => {
    e.preventDefault();

    const {
      dispatch,
      form,
      personnel: {
        data: { pagination },
      },
    } = this.props;
    const { showListMode } = this.state;

    form.validateFields((err, fieldsValue) => {
      if (err) return;

      const values = {
        ...fieldsValue,
        updatedAt: fieldsValue.updatedAt && fieldsValue.updatedAt.valueOf(),
      };
      const val = values.name ? values : {};

      dispatch({
        type: 'personnel/getList',
        payload: {
          ...pagination,
          ...val,
          showListMode,
        },
      });
    });
  };

  /**
   * 排序，筛选，翻页
   *
   * @memberof TableList
   */
  handleStandardTableChange = (pagination, filtersArg, sorter) => {
    const { dispatch } = this.props;
    const { showListMode } = this.state;

    // const filters = Object.keys(filtersArg).reduce((obj, key) => {
    //   const newObj = { ...obj };
    //   newObj[key] = getValue(filtersArg[key]);
    //   return newObj;
    // }, {});

    const params = {
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
      // ...filters,
      showListMode,
    };
    if (sorter.field) {
      params.sorter = `${sorter.field}_${sorter.order}`;
    }

    dispatch({
      type: 'personnel/getList',
      payload: params,
    });
  };

  /**
   * 删除用户
   *
   * @param {Array} list
   */
  handlerRemove = list => {
    const {
      dispatch,
      personnel: {
        data: { pagination },
      },
    } = this.props;
    const { showListMode, selectedRows } = this.state;
    const numList =
      list ||
      selectedRows.map(row => {
        return row.num;
      });

    showConfirm('是否确认删除', '删除操作不可逆，请谨慎操作！', () => {
      dispatch({
        type: 'personnel/removeList',
        payload: Object.assign({}, pagination, { numList }),
        callback: () => {
          message.success('删除成功');
          dispatch({
            type: 'personnel/getList',
            payload: pagination,
            showListMode,
          });
        },
      });
    });
  };

  /**
   * 模糊查询组件
   *
   * @returns
   * @memberof TableList
   */
  serchForm() {
    const {
      form: { getFieldDecorator },
    } = this.props;

    return (
      <Form onSubmit={this.handleSearch} layout="inline">
        <FormItem>
          {getFieldDecorator('name')(<Input placeholder="请输入人员代码或姓名" />)}
        </FormItem>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
      </Form>
    );
  }

  render() {
    const {
      personnel: { data },
      loading,
    } = this.props;
    const { selectedRows, updateModal } = this.state;

    // 弹出窗的相关方法
    const updateMethods = {
      handleUpdateModalVisible: this.handleUpdateModalVisible, // 切换显示状态
      handleUpdateModalUserlevel: this.handleUpdateModalUserlevel, // 改变用户级别
      handleSaveBaseInfo: this.handleSaveBaseInfo, // 保存基础信息
      handleSaveTr_tInfo: this.handleSaveTr_tInfo, // 保存待遇协议信息
      handleSaveTh_tInfo: this.handleSaveTh_tInfo, // 保存论文目标信息
      handleSavePr_tInfo: this.handleSavePr_tInfo, // 保存项目目标信息
      handleSaveOt_tInfo: this.handleSaveOt_tInfo, // 保存其他目标信息
    };

    return (
      <PageHeaderWrapper title="人才列表">
        <Card bordered={false}>
          <div className={styles.tableList}>
            <Row>
              <Col md={8} sm={24}>
                <div className={styles.tableListOperator}>
                  <Button
                    icon="plus"
                    type="primary"
                    onClick={() => this.handleUpdateModalVisible(true, '新建用户')}
                  >
                    新建
                  </Button>
                  {selectedRows.length > 0 && (
                    <span>
                      <Button onClick={() => this.handlerRemove()}>批量删除</Button>
                    </span>
                  )}
                </div>
              </Col>
              <Col md={16} sm={24}>
                <div className={styles.tableListForm}>{this.serchForm()}</div>
              </Col>
            </Row>

            <StandardTable
              className={styles.tableListTable}
              rowKey="num"
              selectedRows={selectedRows}
              loading={loading}
              data={data}
              columns={this.columns}
              hasrowSelection={false}
              onSelectRow={this.handleSelectRows}
              onChange={this.handleStandardTableChange}
            />
          </div>
        </Card>
        <UpdateForm {...updateModal} {...updateMethods} />
      </PageHeaderWrapper>
    );
  }
}

export default TableList;
