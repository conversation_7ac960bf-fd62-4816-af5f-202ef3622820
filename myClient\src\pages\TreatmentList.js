/* eslint-disable no-underscore-dangle */
/* eslint-disable no-eval */
/* eslint-disable camelcase */
import React, { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import router from 'umi/router';
import { Card, Row, Col, Form, Input, DatePicker, Button, Divider, Modal, message } from 'antd';
import StandardTable from '@/components/StandardTable';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import DynamicList from '@/components/Custom/DynamicList';
import { isArray } from 'util';

import styles from './TreatmentList.less';

const FormItem = Form.Item;

const dateFormat = 'YYYY/MM/DD';

const { confirm } = Modal;
/**
 * 全局方法，弹出确认窗口
 *
 * @param {String} title 标题
 * @param {String} content 描述
 * @param {Function} onOk 确认回调
 * @param {Function} onCancel 取消回调
 */
function showConfirm(title, content, onOk, onCancel) {
  confirm({
    title,
    content,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      if (typeof onOk === 'function') {
        onOk();
      }
    },
    onCancel: () => {
      if (typeof onCancel === 'function') {
        onCancel();
      }
    },
  });
}

/**
 * 计算以数据方式存储字段的汇总值
 *
 * @param {String} val
 * @returns
 */
const getSum = val => {
  const arr = eval(val);
  if (isArray(arr)) {
    let sum = 0;
    arr.map(item => {
      const v = parseFloat(item[1]);
      if (!Number.isNaN(v)) {
        sum += v;
      }
      return null;
    });
    return sum;
  }
  return val;
};

@Form.create()
class UpdateForm extends PureComponent {
  formLayout = {
    layout: 'vertical',
  };

  formItemLayout = {
    style: {
      width: '100%',
    },
  };

  /**
   * 把数据更新到表单真正的dom中
   *
   * @param {*} value
   * @param {*} id
   */
  updateRealValue = (value, id) => {
    const { form } = this.props;

    const json = {};
    json[id] = JSON.stringify(value);
    form.setFieldsValue(json);
  };

  /**
   * 基础信息表单
   *
   * @returns
   * @memberof UpdateForm
   */
  editForm() {
    const {
      form: { getFieldDecorator },
      values,
      infoMode,
      handleSaveInfo,
    } = this.props;

    const hasValue = infoMode === 'edit' || infoMode === 'view';
    const canEdit = infoMode === 'create' || infoMode === 'edit';

    const dynamicConStyle = {
      margin: '-15px 0px 10px',
      border: '1px solid #e8e8e8',
      padding: '5px',
      borderRadius: '5px',
    };

    return (
      <Form
        {...this.formLayout}
        onSubmit={e => {
          e.preventDefault();
          const { form } = this.props;
          handleSaveInfo(form, infoMode);
        }}
      >
        <FormItem {...this.formItemLayout}>
          {getFieldDecorator('_id', {
            initialValue: hasValue ? values._id : '',
          })(<Input type="hidden" />)}
        </FormItem>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="人员代码" required>
              {getFieldDecorator('num', {
                initialValue: hasValue ? values.num : '',
                rules: [{ required: true, message: '不能为空！' }],
              })(<Input disabled={hasValue} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="姓名">
              {getFieldDecorator('name', {
                initialValue: hasValue ? values.user_id.name : '',
              })(<Input disabled />)}
            </FormItem>
          </Col>
        </Row>
        <Divider orientation="left" />
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={24}>
            <FormItem {...this.formItemLayout} label="安家费">
              {getFieldDecorator('settling_in_allowance', {
                initialValue: hasValue ? values.settling_in_allowance : '',
              })(<Input type="hidden" disabled={hasValue} />)}
            </FormItem>
            <div style={dynamicConStyle}>
              {hasValue ? (
                <DynamicList
                  array={eval(values.settling_in_allowance)}
                  canEdit={canEdit}
                  addonAfter="万元"
                  handlerUpdateValue={val => {
                    this.updateRealValue(val, 'settling_in_allowance');
                  }}
                />
              ) : (
                <DynamicList
                  array={[]}
                  canEdit={canEdit}
                  addonAfter="万元"
                  handlerUpdateValue={val => {
                    this.updateRealValue(val, 'settling_in_allowance');
                  }}
                />
              )}
            </div>
          </Col>
          <Col span={24}>
            <FormItem {...this.formItemLayout} label="科研经费">
              {getFieldDecorator('scientific_research_funds', {
                initialValue: hasValue ? values.scientific_research_funds : '',
              })(<Input type="hidden" disabled={hasValue} />)}
            </FormItem>
            <div style={dynamicConStyle}>
              {hasValue ? (
                <DynamicList
                  array={eval(values.scientific_research_funds)}
                  canEdit={canEdit}
                  addonAfter="万元"
                  handlerUpdateValue={val => {
                    this.updateRealValue(val, 'scientific_research_funds');
                  }}
                />
              ) : (
                <DynamicList
                  array={[]}
                  canEdit={canEdit}
                  addonAfter="万元"
                  handlerUpdateValue={val => {
                    this.updateRealValue(val, 'scientific_research_funds');
                  }}
                />
              )}
            </div>
          </Col>
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={24}>
            <FormItem {...this.formItemLayout} label="实验室建设费">
              {getFieldDecorator('lab_construction_fee', {
                initialValue: hasValue ? values.lab_construction_fee : '',
              })(<Input type="hidden" disabled={hasValue} />)}
            </FormItem>
            <div style={dynamicConStyle}>
              {hasValue ? (
                <DynamicList
                  array={eval(values.lab_construction_fee)}
                  canEdit={canEdit}
                  addonAfter="万元"
                  handlerUpdateValue={val => {
                    this.updateRealValue(val, 'lab_construction_fee');
                  }}
                />
              ) : (
                <DynamicList
                  array={[]}
                  canEdit={canEdit}
                  addonAfter="万元"
                  handlerUpdateValue={val => {
                    this.updateRealValue(val, 'lab_construction_fee');
                  }}
                />
              )}
            </div>
          </Col>
          <Col span={24}>
            <FormItem {...this.formItemLayout} label="购房补贴">
              {getFieldDecorator('housing_subsidies', {
                initialValue: hasValue ? values.housing_subsidies : '',
              })(<Input type="hidden" disabled={hasValue} />)}
            </FormItem>
            <div style={dynamicConStyle}>
              {hasValue ? (
                <DynamicList
                  array={eval(values.housing_subsidies)}
                  canEdit={canEdit}
                  addonAfter="万元"
                  handlerUpdateValue={val => {
                    this.updateRealValue(val, 'housing_subsidies');
                  }}
                />
              ) : (
                <DynamicList
                  array={[]}
                  canEdit={canEdit}
                  addonAfter="万元"
                  handlerUpdateValue={val => {
                    this.updateRealValue(val, 'housing_subsidies');
                  }}
                />
              )}
            </div>
          </Col>
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="住房">
              {getFieldDecorator('housing', { initialValue: hasValue ? values.housing : '' })(
                <Input disabled={!canEdit} />
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="住房分配时间">
              {getFieldDecorator('housing_date', {
                initialValue:
                  hasValue && values.housing_date
                    ? moment(new Date(values.housing_date), dateFormat)
                    : null,
              })(<DatePicker format={dateFormat} disabled={!canEdit} />)}
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="过渡住房">
              {getFieldDecorator('makeshift_house', {
                initialValue: hasValue ? values.makeshift_house : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="过渡住房分配时间">
              {getFieldDecorator('makeshift_house_date', {
                initialValue:
                  hasValue && values.makeshift_house_date
                    ? moment(new Date(values.makeshift_house_date), dateFormat)
                    : null,
              })(<DatePicker format={dateFormat} disabled={!canEdit} />)}
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="科研用房">
              {getFieldDecorator('scientific_research_house', {
                initialValue: hasValue ? values.scientific_research_house : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="科研用房分配时间">
              {getFieldDecorator('srh_date', {
                initialValue:
                  hasValue && values.srh_date
                    ? moment(new Date(values.srh_date), dateFormat)
                    : null,
              })(<DatePicker format={dateFormat} disabled={!canEdit} />)}
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="职称特评">
              {getFieldDecorator('title_tp', {
                initialValue: hasValue ? values.title_tp : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="职称特评时间">
              {getFieldDecorator('title_tp_date', {
                initialValue:
                  hasValue && values.title_tp_date
                    ? moment(new Date(values.title_tp_date), dateFormat)
                    : null,
              })(<DatePicker format={dateFormat} disabled={!canEdit} />)}
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={24}>
            <FormItem {...this.formItemLayout} label="配偶工作">
              {getFieldDecorator('spouse_work', {
                initialValue: hasValue ? values.spouse_work : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="配偶工作安排时间">
              {getFieldDecorator('spouse_work_date', {
                initialValue:
                  hasValue && values.spouse_work_date
                    ? moment(new Date(values.spouse_work_date), dateFormat)
                    : null,
              })(<DatePicker format={dateFormat} disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout} label="备注">
              {getFieldDecorator('remark', { initialValue: hasValue ? values.remark : '' })(
                <Input disabled={!canEdit} />
              )}
            </FormItem>
          </Col>
        </Row>

        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={24} style={{ textAlign: 'right' }}>
            {canEdit ? (
              <Button type="primary" style={{ margin: '1em', width: '10em' }} htmlType="submit">
                保存
              </Button>
            ) : (
              ''
            )}
          </Col>
        </Row>
      </Form>
    );
  }

  render() {
    const { visible, title, values, handleUpdateModalVisible } = this.props;

    return (
      <Modal
        className={styles.updateForm}
        width={640}
        bodyStyle={{ padding: '32px 40px 48px' }}
        destroyOnClose
        title={title}
        visible={visible}
        maskClosable={false}
        footer={null}
        onCancel={() => handleUpdateModalVisible(false, title, values)}
        // afterClose={() => handleUpdateModalVisible()}
      >
        <div>{this.editForm()}</div>
      </Modal>
    );
  }
}

/* eslint react/no-multi-comp:0 */
@connect(({ treatment_r, loading }) => ({
  treatment_r,
  loading: loading.models.treatment_r,
}))
@Form.create()
class TableList extends PureComponent {
  state = {
    selectedRows: [], // 选中行
    updateModal: {
      // 信息窗口
      visible: false, // 显示状态
      title: '', // 标题
      values: {}, // 内容
      infoMode: 'create', // 基础信息编辑状态, create|edit|view
    },
  };

  // 表列配置
  columns = [
    {
      title: '人员代码',
      dataIndex: 'num',
      // sorter: true,
      render: text => (
        <a
          onClick={() => {
            router.push(`/jbqkb?num=${text}`);
          }}
        >
          {text}
        </a>
      ),
    },
    {
      title: '姓名',
      dataIndex: 'user_id.name',
      // sorter: true,
    },
    {
      title: '安家费',
      dataIndex: 'settling_in_allowance',
      render: val => <span>{getSum(val)}</span>,
    },
    {
      title: '科研经费',
      dataIndex: 'scientific_research_funds',
      render: val => <span>{getSum(val)}</span>,
    },
    {
      title: '实验室建设费',
      dataIndex: 'lab_construction_fee',
      render: val => <span>{getSum(val)}</span>,
    },
    {
      title: '购房补贴',
      dataIndex: 'housing_subsidies',
      render: val => <span>{getSum(val)}</span>,
    },
    {
      title: '科研用房',
      dataIndex: 'scientific_research_house',
    },
    {
      title: '操作',
      render: (text, record) => (
        <Fragment>
          <a onClick={() => this.handleUpdateModalVisible(true, '待遇兑现详情', record, 'view')}>
            查看
          </a>
          <Divider type="vertical" />
          <a onClick={() => this.handleUpdateModalVisible(true, '待遇兑现详情', record, 'edit')}>
            修改
          </a>
          <Divider type="vertical" />
          <a
            onClick={e => {
              e.preventDefault();
              this.handlerRemove([record._id]);
            }}
          >
            删除
          </a>
        </Fragment>
      ),
    },
  ];

  componentDidMount() {
    const {
      dispatch,
      treatment_r: {
        data: { pagination },
      },
    } = this.props;
    dispatch({
      type: 'treatment_r/getList',
      payload: pagination,
    });
  }

  /**
   * 控制编辑用户信息组件显示状态
   *
   * @param {*} flag
   * @param {*} title
   * @param {*} record
   * @param {*} infoMode
   */
  handleUpdateModalVisible = (flag, title, record, infoMode) => {
    const updateModal = {
      visible: !!flag,
      title,
      values: record || {},
      infoMode: infoMode || 'create',
    };
    this.setState({
      updateModal,
    });
  };

  /**
   * 保存用户基础信息，自动判断新增是更新
   *
   * @param {Object} form 表单对象
   * @param {String} infoMode 提交模式，新增或更新
   */
  handleSaveInfo = (form, infoMode) => {
    const {
      dispatch,
      treatment_r: {
        data: { pagination },
      },
    } = this.props;

    if (form) {
      form.validateFields((err, fieldsValue) => {
        if (err) return;

        const values = {
          ...fieldsValue,
        };

        const saveUrl = infoMode === 'edit' ? 'treatment_r/update' : 'treatment_r/create';
        dispatch({
          type: saveUrl,
          payload: values,
          callback: result => {
            if (result.status === 'ok') {
              message.success('保存成功');
              dispatch({
                type: 'treatment_r/getList',
                payload: pagination,
              });
              if (infoMode === 'create') {
                const { updateModal } = this.state;
                this.setState({
                  updateModal: {
                    ...updateModal,
                    title: '待遇兑现详情',
                    values: result.result,
                    infoMode: 'edit',
                  },
                });
              }
            } else {
              message.error(result.message);
            }
          },
        });
      });
    }
  };

  /**
   * 选中行
   *
   * @param {Array} rows
   */
  handleSelectRows = rows => {
    this.setState({
      selectedRows: rows,
    });
  };

  /**
   * 模糊查询
   *
   * @param {Event} e
   */
  handleSearch = e => {
    e.preventDefault();

    const {
      dispatch,
      form,
      treatment_r: {
        data: { pagination },
      },
    } = this.props;

    form.validateFields((err, fieldsValue) => {
      if (err) return;

      const values = {
        ...fieldsValue,
        updatedAt: fieldsValue.updatedAt && fieldsValue.updatedAt.valueOf(),
      };

      dispatch({
        type: 'treatment_r/getList',
        payload: Object.assign({}, pagination, values.name && values),
      });
    });
  };

  /**
   * 排序，筛选，翻页
   *
   * @memberof TableList
   */
  handleStandardTableChange = (pagination, filtersArg, sorter) => {
    const { dispatch } = this.props;
    const { formValues } = this.state;

    // TODO: 筛选未实现
    // const filters = Object.keys(filtersArg).reduce((obj, key) => {
    // const newObj = { ...obj };
    // newObj[key] = getValue(filtersArg[key]);
    // return newObj;
    // }, {});

    const params = {
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
      ...formValues,
      // ...filters,
    };
    if (sorter.field) {
      params.sorter = `${sorter.field}_${sorter.order}`;
    }

    dispatch({
      type: 'treatment_r/getList',
      payload: params,
    });
  };

  /**
   * 删除
   *
   * @param {Array} list
   */
  handlerRemove = list => {
    const {
      dispatch,
      treatment_r: {
        data: { pagination },
      },
    } = this.props;
    const { selectedRows } = this.state;
    const _idList =
      list ||
      selectedRows.map(row => {
        return row._id;
      });

    showConfirm('是否确认删除', '删除操作不可逆，请谨慎操作！', () => {
      dispatch({
        type: 'treatment_r/removeList',
        payload: { _idList },
        callback: () => {
          message.success('删除成功');
          dispatch({
            type: 'treatment_r/getList',
            payload: pagination,
          });
        },
      });
    });
  };

  /**
   * 模糊查询组件
   *
   * @returns
   * @memberof TableList
   */
  serchForm() {
    const {
      form: { getFieldDecorator },
    } = this.props;

    return (
      <Form onSubmit={this.handleSearch} layout="inline">
        <FormItem>
          {getFieldDecorator('name')(<Input placeholder="请输入人员代码或姓名" />)}
        </FormItem>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
      </Form>
    );
  }

  render() {
    const {
      treatment_r: { data },
      loading,
    } = this.props;
    const { selectedRows, updateModal } = this.state;

    // 弹出窗的相关方法
    const updateMethods = {
      handleUpdateModalVisible: this.handleUpdateModalVisible, // 切换显示状态
      handleSaveInfo: this.handleSaveInfo, // 保存基础信息
    };

    return (
      <PageHeaderWrapper title="待遇兑现表">
        <Card bordered={false}>
          <div className={styles.tableList}>
            <Row>
              <Col md={8} sm={24}>
                <div className={styles.tableListOperator}>
                  <Button
                    icon="plus"
                    type="primary"
                    onClick={() => this.handleUpdateModalVisible(true, '新建记录')}
                  >
                    新建
                  </Button>
                  {selectedRows.length > 0 && (
                    <span>
                      <Button onClick={() => this.handlerRemove()}>批量删除</Button>
                    </span>
                  )}
                </div>
              </Col>
              <Col md={16} sm={24}>
                <div className={styles.tableListForm}>{this.serchForm()}</div>
              </Col>
            </Row>

            <StandardTable
              className={styles.tableListTable}
              rowKey="_id"
              selectedRows={selectedRows}
              loading={loading}
              data={data}
              columns={this.columns}
              hasrowSelection={false}
              onSelectRow={this.handleSelectRows}
              onChange={this.handleStandardTableChange}
            />
          </div>
        </Card>
        <UpdateForm {...updateModal} {...updateMethods} />
      </PageHeaderWrapper>
    );
  }
}

export default TableList;
