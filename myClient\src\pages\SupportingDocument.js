/* eslint-disable camelcase */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-underscore-dangle */
import React, { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { Card, Row, Col, Form, Input, DatePicker, Button, Divider, Modal, message } from 'antd';
import StandardTable from '@/components/StandardTable';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import MyUpload from '@/components/Custom/MyUpload';

import styles from './SupportingDocument.less';

const FormItem = Form.Item;

const timeFormat = 'YYYY/MM/DD';

const { confirm } = Modal;
/**
 * 全局方法，弹出确认窗口
 *
 * @param {String} title 标题
 * @param {String} content 描述
 * @param {Function} onOk 确认回调
 * @param {Function} onCancel 取消回调
 */
function showConfirm(title, content, onOk, onCancel) {
  confirm({
    title,
    content,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      if (typeof onOk === 'function') onOk();
    },
    onCancel: () => {
      if (typeof onCancel === 'function') onCancel();
    },
  });
}

@Form.create()
class UpdateForm extends PureComponent {
  formLayout = {
    layout: 'vertical',
    // layout: 'inline',
  };

  formItemLayout_24 = {
    style: {
      width: '100%',
      marginBottom: '5px',
    },
  };

  formItemLayout_12 = {
    style: {
      width: '100%',
      marginBottom: '5px',
    },
    // labelCol: {
    //   xs: { span: 24 },
    //   sm: { span: 8 },
    // },
    // wrapperCol: {
    //   xs: { span: 24 },
    //   sm: { span: 16 },
    // },
  };

  /**
   * 把数据更新到表单真正的dom中
   *
   * @param {*} value
   * @param {*} id
   */
  updateRealValue = (value, id) => {
    const { form } = this.props;

    const json = {};
    json[id] = typeof value === 'string' ? value : JSON.stringify(value);
    form.setFieldsValue(json);
  };

  /**
   * 基础信息表单
   *
   * @returns
   * @memberof UpdateForm
   */
  editForm() {
    const {
      form: { getFieldDecorator, getFieldValue },
      values,
      infoMode,
      handleSaveInfo,
    } = this.props;

    const hasValue = infoMode === 'edit' || infoMode === 'view';
    const canEdit = infoMode === 'create' || infoMode === 'edit';

    return (
      <Form
        {...this.formLayout}
        onSubmit={e => {
          e.preventDefault();
          const { form } = this.props;
          handleSaveInfo(form, infoMode);
        }}
      >
        <FormItem style={{ padding: 0, height: 0, margin: 0 }}>
          {getFieldDecorator('_id', {
            initialValue: hasValue ? values._id : '',
          })(<Input type="hidden" />)}
        </FormItem>
        <FormItem style={{ padding: 0, height: 0, margin: 0 }}>
          {getFieldDecorator('link', {
            initialValue: hasValue ? values.link : '',
          })(<Input type="hidden" />)}
        </FormItem>
        <FormItem style={{ padding: 0, height: 0, margin: 0 }}>
          {getFieldDecorator('path', {
            initialValue: hasValue ? values.path : '',
          })(<Input type="hidden" />)}
        </FormItem>
        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={24}>
            <FormItem {...this.formItemLayout_24} label="标题" required>
              {getFieldDecorator('title', {
                initialValue: hasValue ? values.title : '',
                rules: [{ required: true, message: '不能为空！' }],
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={24}>
            <FormItem {...this.formItemLayout_24} label="文件" required>
              <MyUpload
                disabled={hasValue}
                action="/server/api/upload"
                fileList={
                  hasValue
                    ? [
                        {
                          uid: '-1',
                          name: values.path
                            .replace(/\\/g, '/')
                            .split('/')
                            .pop()
                            .split('.')[0],
                          status: 'done',
                          url: values.link,
                        },
                      ]
                    : []
                }
                onSuccess={res => {
                  const { name, url, path } = res;
                  if (getFieldValue('title').trim() === '') {
                    const temp = name.split('.');
                    temp.pop();
                    const title = temp.join('.');
                    this.updateRealValue(title, 'title');
                  }
                  this.updateRealValue(url, 'link');
                  this.updateRealValue(path, 'path');
                }}
                onError={() => {}}
              />
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout_12} label="类型">
              {getFieldDecorator('type', {
                initialValue: hasValue ? values.type : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout_12} label="关键字">
              {getFieldDecorator('keyword', {
                initialValue: hasValue ? values.keyword : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout_12} label="上传人">
              {getFieldDecorator('uploadUser', {
                initialValue: hasValue ? values.uploadUser : '',
              })(<Input disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem {...this.formItemLayout_12} label="上传时间">
              {getFieldDecorator('uploadTime', {
                initialValue:
                  hasValue && values.uploadTime
                    ? moment(new Date(values.uploadTime), timeFormat)
                    : infoMode === 'create'
                    ? moment(new Date(), timeFormat)
                    : null,
              })(<DatePicker format={timeFormat} disabled={!canEdit} />)}
            </FormItem>
          </Col>
          <Col span={24}>
            <FormItem {...this.formItemLayout_24} label="备注">
              {getFieldDecorator('remark', { initialValue: hasValue ? values.remark : '' })(
                <Input disabled={!canEdit} />
              )}
            </FormItem>
          </Col>
        </Row>

        <Row gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}>
          <Col span={24} style={{ textAlign: 'right' }}>
            {canEdit ? (
              <Button type="primary" style={{ margin: '1em', width: '10em' }} htmlType="submit">
                保存
              </Button>
            ) : (
              ''
            )}
          </Col>
        </Row>
      </Form>
    );
  }

  render() {
    const { visible, title, values, handleUpdateModalVisible } = this.props;

    return (
      <Modal
        className={styles.updateForm}
        width={640}
        bodyStyle={{ padding: '32px 40px 48px' }}
        destroyOnClose
        title={title}
        visible={visible}
        maskClosable={false}
        footer={null}
        onCancel={() => handleUpdateModalVisible(false, title, values)}
        // afterClose={() => handleUpdateModalVisible()}
      >
        {this.editForm()}
      </Modal>
    );
  }
}

/* eslint react/no-multi-comp:0 */
@connect(({ supportingDoc, loading }) => ({
  supportingDoc,
  loading: loading.models.supportingDoc,
}))
@Form.create()
class TableList extends PureComponent {
  state = {
    selectedRows: [], // 选中行
    updateModal: {
      // 信息窗口
      visible: false, // 显示状态
      title: '', // 标题
      values: {}, // 内容
      infoMode: 'create', // 基础信息编辑状态, create|edit|view
    },
  };

  columns = [
    {
      title: '标题',
      dataIndex: 'title',
      // sorter: true,
      render: (text, record) => (
        <div>
          <a href={record.link} target="_black">
            {text}
          </a>
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      // sorter: true,
    },
    {
      title: '关键字',
      dataIndex: 'keyword',
    },
    {
      title: '上传人',
      dataIndex: 'uploadUser',
    },
    {
      title: '上传时间',
      dataIndex: 'uploadTime',
      render: text => {
        if (text) {
          const time = new Date(text);
          return `${time.getFullYear()}年${time.getMonth() +
            1}月${time.getDate()} ${time.getHours()}:${time.getMinutes()}`;
        }
        return '';
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    {
      title: '操作',
      render: (text, record) => (
        <Fragment>
          <a onClick={() => this.handleUpdateModalVisible(true, '修改记录', record, 'edit')}>
            修改
          </a>
          <Divider type="vertical" />
          <a
            onClick={e => {
              e.preventDefault();
              this.handlerRemove([record._id]);
            }}
          >
            删除
          </a>
        </Fragment>
      ),
    },
  ];

  componentDidMount() {
    const {
      dispatch,
      supportingDoc: {
        data: { pagination },
      },
    } = this.props;
    dispatch({
      type: 'supportingDoc/getList',
      payload: pagination,
    });
  }

  /**
   * 控制编辑用户信息组件显示状态
   *
   * @param {*} flag
   * @param {*} title
   * @param {*} record
   * @param {*} infoMode
   */
  handleUpdateModalVisible = (flag, title, record, infoMode) => {
    const updateModal = {
      visible: !!flag,
      title,
      values: record || {},
      infoMode: infoMode || 'create',
    };
    this.setState({
      updateModal,
    });
  };

  /**
   * 保存用户基础信息，自动判断新增是更新
   *
   * @param {Object} form 表单对象
   * @param {String} infoMode 提交模式，新增或更新
   */
  handleSaveInfo = (form, infoMode) => {
    const {
      dispatch,
      supportingDoc: {
        data: { pagination },
      },
    } = this.props;

    if (form) {
      form.validateFields((err, fieldsValue) => {
        if (err) return;

        const values = {
          ...fieldsValue,
        };

        const saveUrl = infoMode === 'edit' ? 'supportingDoc/update' : 'supportingDoc/create';
        dispatch({
          type: saveUrl,
          payload: values,
          callback: result => {
            if (result.status === 'ok') {
              message.success('保存成功');
              dispatch({
                type: 'supportingDoc/getList',
                payload: pagination,
              });
              // 新建状态下进行保存后立即切换为编辑状态，以防止多次点击保存创建多个对象
              const { updateModal } = this.state;
              if (infoMode === 'create') {
                this.setState({
                  updateModal: {
                    ...updateModal,
                    visible: false, // 显示状态visible: false, // 显示状态
                    title: '其他成果详情',
                    values: result.result,
                    infoMode: 'edit',
                  },
                });
              } else {
                this.setState({
                  updateModal: {
                    // 信息窗口
                    ...updateModal,
                    visible: false, // 显示状态
                  },
                });
              }
            } else {
              message.error(result.message);
            }
          },
        });
      });
    }
  };

  /**
   * 选中行
   *
   * @param {Array} rows
   */
  handleSelectRows = rows => {
    this.setState({
      selectedRows: rows,
    });
  };

  /**
   * 模糊查询
   *
   * @param {Event} e
   */
  handleSearch = e => {
    e.preventDefault();

    const {
      dispatch,
      form,
      supportingDoc: {
        data: { pagination },
      },
    } = this.props;

    form.validateFields((err, fieldsValue) => {
      if (err) return;

      const values = {
        ...fieldsValue,
        updatedAt: fieldsValue.updatedAt && fieldsValue.updatedAt.valueOf(),
      };

      dispatch({
        type: 'supportingDoc/getList',
        payload: Object.assign({}, pagination, values.title && values),
      });
    });
  };

  /**
   * 排序，筛选，翻页
   *
   * @memberof TableList
   */
  handleStandardTableChange = (pagination, filtersArg, sorter) => {
    const { dispatch } = this.props;
    const { formValues } = this.state;

    // TODO: 筛选未实现
    // const filters = Object.keys(filtersArg).reduce((obj, key) => {
    //   const newObj = { ...obj };
    //   newObj[key] = getValue(filtersArg[key]);
    //   return newObj;
    // }, {});

    const params = {
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
      ...formValues,
      // ...filters,
    };
    if (sorter.field) {
      params.sorter = `${sorter.field}_${sorter.order}`;
    }

    dispatch({
      type: 'supportingDoc/getList',
      payload: params,
    });
  };

  /**
   * 删除
   *
   * @param {Array} list
   */
  handlerRemove = list => {
    const {
      dispatch,
      supportingDoc: {
        data: { pagination },
      },
    } = this.props;
    const { selectedRows } = this.state;
    const _idList =
      list ||
      selectedRows.map(row => {
        return row._id;
      });

    showConfirm('是否确认删除', '删除操作不可逆，请谨慎操作！', () => {
      dispatch({
        type: 'supportingDoc/removeList',
        payload: { _idList },
        callback: () => {
          message.success('删除成功');
          dispatch({
            type: 'supportingDoc/getList',
            payload: pagination,
          });
        },
      });
    });
  };

  /**
   * 模糊查询组件
   *
   * @returns
   * @memberof TableList
   */
  serchForm() {
    const {
      form: { getFieldDecorator },
    } = this.props;

    return (
      <Form onSubmit={this.handleSearch} layout="inline">
        <FormItem>{getFieldDecorator('title')(<Input placeholder="请输入标题" />)}</FormItem>
        <Button type="primary" htmlType="submit">
          查询
        </Button>
      </Form>
    );
  }

  render() {
    const {
      supportingDoc: { data },
      loading,
    } = this.props;
    const { selectedRows, updateModal } = this.state;

    // 弹出窗的相关方法
    const updateMethods = {
      handleUpdateModalVisible: this.handleUpdateModalVisible, // 切换显示状态
      handleSaveInfo: this.handleSaveInfo, // 保存基础信息
    };

    return (
      <PageHeaderWrapper title="其他成果表">
        <Card bordered={false}>
          <div className={styles.tableList}>
            <Row>
              <Col md={8} sm={24}>
                <div className={styles.tableListOperator}>
                  <Button
                    icon="plus"
                    type="primary"
                    onClick={() => this.handleUpdateModalVisible(true, '新建记录')}
                  >
                    新建
                  </Button>
                  {selectedRows.length > 0 && (
                    <span>
                      <Button onClick={() => this.handlerRemove()}>批量删除</Button>
                    </span>
                  )}
                </div>
              </Col>
              <Col md={16} sm={24}>
                <div className={styles.tableListForm}>{this.serchForm()}</div>
              </Col>
            </Row>

            <StandardTable
              className={styles.tableListTable}
              rowKey="_id"
              selectedRows={selectedRows}
              loading={loading}
              data={data}
              columns={this.columns}
              hasrowSelection={false}
              onSelectRow={this.handleSelectRows}
              onChange={this.handleStandardTableChange}
            />
          </div>
        </Card>
        <UpdateForm {...updateModal} {...updateMethods} />
      </PageHeaderWrapper>
    );
  }
}

export default TableList;
