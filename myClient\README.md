# 西安科技大学高层次人才服务管理平台

## 项目简介
本项目是基于 Ant Design Pro 开发的西安科技大学高层次人才服务管理平台，用于管理和服务高层次人才的综合性Web应用。

## 主要功能
- 人才信息管理
- 待遇兑现管理
- 成果管理（论文、项目、其他成果）
- 数据统计与分析
- 汇总导出功能
- 多语言支持（中文简体、中文繁体、英文）

## 技术栈
- **前端框架**: React 16.7.0
- **UI组件库**: Ant Design 3.17.0
- **状态管理**: Dva 2.4.1
- **构建工具**: UmiJS 2.7.3
- **图表库**: BizCharts 3.4.3
- **样式**: Less
- **国际化**: 内置多语言支持

## 环境依赖
- **Node.js**: 建议使用 11.x 版本
- **npm**: 6.x 或更高版本
- **yarn**: 推荐使用 yarn 进行包管理

## 开发注意事项
### 参考文档
https://pro.ant.design/docs/getting-started-cn?tdsourcetag=s_pctim_aiomsg

### Chromium源码被墙问题
默认使用 npm install ，执行 install.js 会下载 Chromium 源码，由于被墙的原因，需要提前手动下载，
全局安装puppeteer，再把 Chromium 源码拷贝到全局 node_modules 中 puppeteer\.local-chromium路径下
```bash
npm install -g puppeteer --ignore-scripts
```
然后执行 npm install 进行编译

## 快速开始

### 安装依赖
```bash
# 使用 yarn (推荐)
yarn install

# 或使用 npm
npm install
```

### 开发环境启动
```bash
# 启动开发服务器
npm run start

# 启动开发服务器（测试环境API）
npm run start:test

# 启动开发服务器（带Mock数据）
npm run start:mock
```

### 构建部署
```bash
# 生产环境构建
npm run build

# 测试环境构建
npm run build:test
```

### 代码检查
```bash
# 运行ESLint检查
npm run lint

# 自动修复代码格式问题
npm run lint:fix
```

### 测试
```bash
# 运行所有测试
npm run test:all

# 运行组件测试
npm run test:component
```

## 项目结构
```
myClient/
├── src/
│   ├── components/     # 公共组件
│   ├── locales/       # 国际化文件
│   ├── models/        # Dva数据模型
│   ├── pages/         # 页面组件
│   └── defaultSettings.js  # 默认配置
├── config/            # 配置文件
├── docker/           # Docker配置
└── package.json      # 项目依赖
```

## 环境配置
项目支持多环境配置：
- **local**: 本地开发环境
- **dev**: 开发测试环境  
- **production**: 生产环境

## Docker部署
```bash
# 构建Docker镜像
npm run docker:build

# 启动Docker容器
npm run docker:dev
```

## 浏览器支持
- Chrome (推荐)
- Firefox
- Safari
- Edge
- IE 11+
