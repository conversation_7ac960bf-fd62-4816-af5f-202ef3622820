import { query, create, update, removeList } from '@/services/treatment_r';

export default {
  namespace: 'treatment_r',

  state: {
    data: {
      list: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
      },
    },
  },

  effects: {
    /**
     * 查询列表
     *
     * @param {*} { payload }
     * @param {*} { call, put }
     */
    *getList({ payload }, { call, put }) {
      const response = yield call(query, payload);

      response.list = Array.isArray(response.list) ? response.list : [];

      yield put({
        type: 'save',
        payload: response,
      });
    },

    /**
     * 创建
     *
     * @param {*} { payload, callback }
     * @param {*} { call }
     */
    *create({ payload, callback }, { call }) {
      const response = yield call(create, payload);

      callback(response);
    },

    /**
     * 更新
     *
     * @param {*} { payload, callback }
     * @param {*} { call }
     */
    *update({ payload, callback }, { call }) {
      const response = yield call(update, payload);

      callback(response);
    },

    /**
     * 批量删除
     *
     * @param {*} { payload, callback }
     * @param {*} { call }
     */
    *removeList({ payload, callback }, { call }) {
      const response = yield call(removeList, payload);

      if (response.status === 'ok' && callback) {
        callback(response);
      }
    },
  },

  reducers: {
    save(state, action) {
      const { payload } = action;
      if (payload && payload.list && payload.list.length) {
        payload.pagination.showTotal = total => `共 ${total} 条记录`;
      }
      return {
        ...state,
        data: payload,
      };
    },
  },
};
