import { stringify } from 'qs';
import request from '@/utils/request';

export default {
  /**
   * 查询列表
   *
   * @param {*} params
   * @returns
   */
  query: async params => {
    // return request(`${SERVER_HOST[API_ENV]}/tr_t_list?${stringify(params)}`);
    return request(`/server/api/tr_t_list?${stringify(params)}`);
  },

  /**
   * 创建记录
   *
   * @param {*} params
   * @returns
   */
  create: async params => {
    // return request(`${SERVER_HOST[API_ENV]}/createTr_t`, {
    return request(`/server/api/createTr_t`, {
      method: 'POST',
      data: params,
    });
  },

  /**
   * 更新
   *
   * @param {*} params
   * @returns
   */
  update: async params => {
    // return request(`${SERVER_HOST[API_ENV]}/updateTr_t`, {
    return request(`/server/api/updateTr_t`, {
      method: 'POST',
      data: params,
    });
  },

  /**
   * 批量删除
   *
   * @param {*} params
   * @returns
   */
  removeList: async params => {
    // return request(`${SERVER_HOST[API_ENV]}/delTr_t_list`, {
    return request(`/server/api/delTr_t_list`, {
      method: 'POST',
      data: params,
    });
  },
};
