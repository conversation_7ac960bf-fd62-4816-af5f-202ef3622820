import RenderAuthorized from '@/components/Authorized';
import { getAuthority, getCurrentUserNum } from './authority';

let Authorized = RenderAuthorized(getAuthority()); // eslint-disable-line

// Reload the rights component
const reloadAuthorized = () => {
  RenderAuthorized(getCurrentUserNum());
  Authorized = RenderAuthorized(getAuthority());
};

export { reloadAuthorized };
export default Authorized;
