@import '~antd/lib/style/themes/default.less';

.descriptionList {
  // offset the padding-bottom of last row
  :global {
    .ant-row {
      margin-bottom: -16px;
      overflow: hidden;
    }
  }
  // fix margin top error of following descriptionList
  & + & {
    :global {
      .ant-row {
        margin-top: 16px;
      }
    }
  }

  .title {
    margin-bottom: 16px;
    color: @heading-color;
    font-weight: 500;
    font-size: 14px;
  }

  .term {
    display: table-cell;
    padding-bottom: 16px;
    color: @heading-color;
    // Line-height is 22px IE dom height will calculate error
    line-height: 20px;
    white-space: nowrap;

    &::after {
      position: relative;
      top: -0.5px;
      margin: 0 8px 0 2px;
      content: ':';
    }
  }

  .detail {
    display: table-cell;
    width: 100%;
    padding-bottom: 16px;
    color: @text-color;
    line-height: 20px;
  }

  &.small {
    // offset the padding-bottom of last row
    :global {
      .ant-row {
        margin-bottom: -8px;
      }
    }
    // fix margin top error of following descriptionList
    & + .descriptionList {
      :global {
        .ant-row {
          margin-top: 8px;
        }
      }
    }
    .title {
      margin-bottom: 12px;
      color: @text-color;
    }
    .term,
    .detail {
      padding-bottom: 8px;
    }
  }

  &.large {
    .title {
      font-size: 16px;
    }
  }

  &.vertical {
    .term {
      display: block;
      padding-bottom: 8px;
    }

    .detail {
      display: block;
    }
  }
}
